import { createRouter, createWebHistory } from 'vue-router'
import LineupManagerView from '@/views/LineupManagerView.vue'
import LineupManagerDraggableView from '@/views/LineupManagerDraggableView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/click-mode'
    },
    {
      path: '/click-mode',
      name: 'click-mode',
      component: LineupManagerView,
      meta: {
        title: 'Click Mode - Baseball Lineup Manager'
      }
    },
    {
      path: '/drag-mode',
      name: 'drag-mode',
      component: LineupManagerDraggableView,
      meta: {
        title: 'Drag & Drop Mode - Baseball Lineup Manager'
      }
    }
  ]
})

// Update page title based on route
router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  next()
})

export default router