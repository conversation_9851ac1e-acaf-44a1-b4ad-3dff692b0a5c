// Type definitions for Lineup Lab application

export type ID = string

// ============================================================================
// DATABASE ENTITIES (Normalized - following Technical Specification)
// ============================================================================

export interface Player {
  id: ID                    // global player id (not tied to team)
  firstName: string
  lastName: string
  throws?: 'L' | 'R' | 'S'  // S = switch
  bats?: 'L' | 'R' | 'S'    // S = switch
  dob?: string | null       // optional
}

export interface Team {
  id: ID
  name: string              // "Dallastown Cougars"
  season: string            // "Fall 2025"
  headCoach?: string
  assistantCoaches?: string[]
  notes?: string
  // Note: players and games are separate entities linked via relationships
}

export interface TeamPlayer {
  id: ID                    // relationship id
  teamId: ID
  playerId: ID
  jerseyNumber?: string | number
  preferredPositions: string[] // e.g. ['P','1B','3B','LF']
  facePhotoId?: ID          // optional reference to Photo table (photo is team-scoped)
}

export interface Photo {
  id: ID
  teamId: ID                // photo belongs to a team snapshot
  playerId?: ID | null      // optional, team-player specific
  filename?: string         // original filename or label
  mimeType?: string
  blob?: Blob | string      // stored blob or base64 (Dexie supports blobs)
  createdAt: string
}

export interface Game {
  id: ID
  teamId: ID
  date: string              // required game date (ISO format)
  gameTime?: string         // scheduled game time (e.g., "6:00 PM")
  arrivalTime?: string      // team arrival time (e.g., "5:30 PM")
  opponent: string          // required opponent team name
  homeAway: 'Home' | 'Away' // required home/away designation
  address?: string          // game location address
  notes?: string            // coach notes, special instructions
  inningCount: number       // number of innings for this game (customizable, typically 12-16)
}

export interface LineupCell {
  id: ID                    // e.g., `${gameId}:i${inning}:pos${positionKey}`
  gameId: ID
  inning: number            // 1..N (N customizable per game)
  positionKey: string       // canonical position id, e.g., 'P','C','1B','Bench1'
  playerId?: ID | null      // assigned player or null
  createdAt: string
  updatedAt?: string
}

// ============================================================================
// UI COMPATIBILITY TYPES (for backward compatibility with existing components)
// ============================================================================

export interface TeamWithRelations {
  id: ID
  name: string
  season: string
  headCoach?: string
  assistantCoaches?: string[]
  notes?: string
  players: Player[]         // denormalized for UI compatibility
  games: Game[]            // denormalized for UI compatibility
}

export interface GridCell {
  gameId: string
  inning: number
  positionKey: string
  playerId?: string | null
}

export interface FocusPosition {
  gameId: string
  inning: number
  positionKey: string
}

export interface ClipboardData {
  playerId: string | null
  operation: 'copy' | 'cut'
  timestamp: number
}

export interface UndoRedoAction {
  id: string
  type: 'assign' | 'clear' | 'move' | 'bulk'
  timestamp: number
  data: {
    cells: GridCell[]
    previousState: GridCell[]
  }
  description: string
}

export interface DragDropData {
  playerId: string
  sourceType: 'roster' | 'cell'
  sourcePosition?: FocusPosition
  operation: 'move' | 'copy'
}

// Standard baseball positions
export const POSITIONS = [
  'P',    // Pitcher
  'C',    // Catcher
  '1B',   // First Base
  '2B',   // Second Base
  '3B',   // Third Base
  'SS',   // Shortstop
  'LF',   // Left Field
  'CF',   // Center Field
  'RF',   // Right Field
] as const

export type Position = typeof POSITIONS[number]

// Generate bench positions dynamically based on roster size
export const getBenchPositions = (rosterSize: number, showBench: boolean = true): string[] => {
  if (!showBench || rosterSize <= 9) return []

  const benchCount = rosterSize - 9
  return Array.from({ length: benchCount }, (_, i) => `Bench${i + 1}`)
}

// Get all positions including bench positions
export const getAllPositions = (rosterSize: number, showBench: boolean = true): string[] => {
  return [...POSITIONS, ...getBenchPositions(rosterSize, showBench)]
}

// Player selection states
export type SelectionState = 'none' | 'single' | 'multi'

// Grid navigation directions
export type NavigationDirection = 'up' | 'down' | 'left' | 'right' | 'home' | 'end'

// Validation result for drag operations
export interface DropValidation {
  isValid: boolean
  message?: string
  requiresConfirmation?: boolean
}

// ============================================================================
// DATABASE-SPECIFIC TYPES
// ============================================================================

// Database schema version for migrations
export interface DatabaseVersion {
  version: number
  description: string
  migrationDate: string
}

// Database initialization status
export interface DatabaseStatus {
  isInitialized: boolean
  version: number
  hasData: boolean
  lastBackup?: string
}

// Data transformation types for migration
export interface DataMigrationResult {
  success: boolean
  playersCreated: number
  teamsCreated: number
  teamPlayersCreated: number
  gamesCreated: number
  lineupCellsCreated: number
  errors: string[]
}

// Export/Import types
export interface DatabaseExport {
  version: number
  exportDate: string
  teams: Team[]
  players: Player[]
  teamPlayers: TeamPlayer[]
  games: Game[]
  lineupCells: LineupCell[]
  photos?: Photo[]
}

// Database query options
export interface QueryOptions {
  limit?: number
  offset?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
}

// Database transaction types
export type TransactionMode = 'readonly' | 'readwrite'

// Service response wrapper
export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}