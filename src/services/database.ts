import Dexie, { type EntityTable } from 'dexie'
import type {
  Player,
  Team,
  TeamPlayer,
  Game,
  LineupCell,
  Photo,
  DatabaseStatus,
  DatabaseVersion
} from '@/types'

// ============================================================================
// DATABASE SCHEMA DEFINITION
// ============================================================================

export class LineupLabDatabase extends Dexie {
  // Database tables
  players!: EntityTable<Player, 'id'>
  teams!: EntityTable<Team, 'id'>
  teamPlayers!: EntityTable<TeamPlayer, 'id'>
  games!: EntityTable<Game, 'id'>
  lineupCells!: EntityTable<LineupCell, 'id'>
  photos!: EntityTable<Photo, 'id'>
  
  // Metadata tables
  versions!: EntityTable<DatabaseVersion, 'version'>

  constructor() {
    super('LineupLabDatabase')

    // Version 1: Initial schema (deprecated - keeping for migration)
    this.version(1).stores({
      players: 'id, firstName, lastName, throws, bats, dob',
      teams: 'id, name, season, headCoach',
      teamPlayers: 'id, teamId, playerId, jerseyNumber, *preferredPositions',
      games: 'id, teamId, date, opponent, homeAway, inningCount',
      lineupCells: 'id, gameId, inning, positionKey, playerId, createdAt',
      photos: 'id, teamId, playerId, createdAt',
      versions: 'version, description, migrationDate'
    })

    // Version 2: Added compound indexes for better query performance
    this.version(2).stores({
      // Core entities with proper compound indexes
      players: 'id, firstName, lastName, throws, bats, dob',
      teams: 'id, name, season, headCoach',
      teamPlayers: 'id, teamId, playerId, [teamId+playerId], [teamId+jerseyNumber], jerseyNumber, *preferredPositions',
      games: 'id, teamId, date, opponent, homeAway, inningCount, [teamId+date]',
      lineupCells: 'id, gameId, inning, positionKey, playerId, [gameId+inning], [gameId+positionKey], [gameId+playerId], createdAt',
      photos: 'id, teamId, playerId, createdAt',

      // Metadata
      versions: 'version, description, migrationDate'
    })

    // Add hooks for automatic timestamps
    this.lineupCells.hook('creating', function (primKey, obj, trans) {
      obj.createdAt = new Date().toISOString()
    })

    this.lineupCells.hook('updating', function (modifications, primKey, obj, trans) {
      modifications.updatedAt = new Date().toISOString()
    })

    this.photos.hook('creating', function (primKey, obj, trans) {
      obj.createdAt = new Date().toISOString()
    })
  }
}

// ============================================================================
// DATABASE INSTANCE AND INITIALIZATION
// ============================================================================

// Singleton database instance
export const db = new LineupLabDatabase()

// Database initialization and status checking
export class DatabaseService {
  private static instance: DatabaseService
  private initializationPromise: Promise<void> | null = null

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }

  /**
   * Initialize the database and ensure it's ready for use
   */
  public async initialize(): Promise<void> {
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this._performInitialization()
    return this.initializationPromise
  }

  private async _performInitialization(): Promise<void> {
    try {
      // Open the database
      await db.open()
      
      // Check if this is the first time opening
      const status = await this.getStatus()
      
      if (!status.isInitialized) {
        await this._performFirstTimeSetup()
      }
      
      console.log('Database initialized successfully')
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw new Error(`Database initialization failed: ${error}`)
    }
  }

  private async _performFirstTimeSetup(): Promise<void> {
    await db.transaction('rw', [db.versions], async () => {
      // Record the current database version
      await db.versions.add({
        version: 2,
        description: 'Database schema with compound indexes for better query performance',
        migrationDate: new Date().toISOString()
      })
    })
  }

  /**
   * Get the current database status
   */
  public async getStatus(): Promise<DatabaseStatus> {
    try {
      const latestVersion = await db.versions
        .orderBy('version')
        .reverse()
        .first()

      const teamCount = await db.teams.count()
      const playerCount = await db.players.count()

      return {
        isInitialized: !!latestVersion,
        version: latestVersion?.version || 0,
        hasData: teamCount > 0 || playerCount > 0,
        lastBackup: undefined // Will be implemented in Phase 4
      }
    } catch (error) {
      console.error('Failed to get database status:', error)
      return {
        isInitialized: false,
        version: 0,
        hasData: false
      }
    }
  }

  /**
   * Clear all data from the database (useful for development/testing)
   */
  public async clearAllData(): Promise<void> {
    await db.transaction('rw', [
      db.players,
      db.teams,
      db.teamPlayers,
      db.games,
      db.lineupCells,
      db.photos
    ], async () => {
      await db.players.clear()
      await db.teams.clear()
      await db.teamPlayers.clear()
      await db.games.clear()
      await db.lineupCells.clear()
      await db.photos.clear()
    })
  }

  /**
   * Close the database connection
   */
  public async close(): Promise<void> {
    await db.close()
    this.initializationPromise = null
  }

  /**
   * Check if the database is ready for use
   */
  public async isReady(): Promise<boolean> {
    try {
      const status = await this.getStatus()
      return status.isInitialized
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance()

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate a composite ID for lineup cells
 */
export function generateLineupCellId(gameId: string, inning: number, positionKey: string): string {
  return `${gameId}:i${inning}:pos${positionKey}`
}

/**
 * Parse a lineup cell ID back into its components
 */
export function parseLineupCellId(id: string): { gameId: string; inning: number; positionKey: string } | null {
  const match = id.match(/^(.+):i(\d+):pos(.+)$/)
  if (!match) return null
  
  return {
    gameId: match[1],
    inning: parseInt(match[2], 10),
    positionKey: match[3]
  }
}

/**
 * Validate database entity before saving
 */
export function validateEntity<T extends { id: string }>(entity: T, requiredFields: (keyof T)[]): string[] {
  const errors: string[] = []
  
  for (const field of requiredFields) {
    if (!entity[field]) {
      errors.push(`Missing required field: ${String(field)}`)
    }
  }
  
  return errors
}
