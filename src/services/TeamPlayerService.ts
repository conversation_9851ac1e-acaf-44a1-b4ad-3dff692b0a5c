import type { TeamPlayer, Player, ServiceResponse } from '@/types'
import { BaseService } from './BaseService'
import { db } from './database'

/**
 * Service for managing TeamPlayer relationship entities
 */
export class TeamPlayerService extends BaseService<TeamPlayer> {
  protected tableName = 'teamPlayers'
  protected requiredFields: (keyof TeamPlayer)[] = ['id', 'teamId', 'playerId']

  /**
   * Get all team players for a specific team
   */
  async getByTeamId(teamId: string): Promise<ServiceResponse<TeamPlayer[]>> {
    try {
      const teamPlayers = await db.teamPlayers
        .where('teamId')
        .equals(teamId)
        .toArray()

      return {
        success: true,
        data: teamPlayers,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get team players: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get all teams for a specific player
   */
  async getByPlayerId(playerId: string): Promise<ServiceResponse<TeamPlayer[]>> {
    try {
      const teamPlayers = await db.teamPlayers
        .where('playerId')
        .equals(playerId)
        .toArray()

      return {
        success: true,
        data: teamPlayers,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get player teams: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get team players with full player details
   */
  async getTeamPlayersWithDetails(teamId: string): Promise<ServiceResponse<(TeamPlayer & { player: Player })[]>> {
    try {
      const teamPlayers = await db.teamPlayers
        .where('teamId')
        .equals(teamId)
        .toArray()

      const playersWithDetails = await Promise.all(
        teamPlayers.map(async (teamPlayer) => {
          const player = await db.players.get(teamPlayer.playerId)
          return {
            ...teamPlayer,
            player: player!
          }
        })
      )

      return {
        success: true,
        data: playersWithDetails,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get team players with details: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Add a player to a team
   */
  async addPlayerToTeam(
    teamId: string, 
    playerId: string, 
    options?: {
      jerseyNumber?: string | number
      preferredPositions?: string[]
    }
  ): Promise<ServiceResponse<TeamPlayer>> {
    try {
      // Check if relationship already exists
      const existing = await db.teamPlayers
        .where(['teamId', 'playerId'])
        .equals([teamId, playerId])
        .first()

      if (existing) {
        return {
          success: false,
          error: 'Player is already on this team',
          timestamp: new Date().toISOString()
        }
      }

      // Check if jersey number is already taken
      if (options?.jerseyNumber) {
        const jerseyTaken = await db.teamPlayers
          .where(['teamId', 'jerseyNumber'])
          .equals([teamId, options.jerseyNumber])
          .first()

        if (jerseyTaken) {
          return {
            success: false,
            error: `Jersey number ${options.jerseyNumber} is already taken`,
            timestamp: new Date().toISOString()
          }
        }
      }

      const teamPlayer: TeamPlayer = {
        id: crypto.randomUUID(),
        teamId,
        playerId,
        jerseyNumber: options?.jerseyNumber,
        preferredPositions: options?.preferredPositions || []
      }

      return await this.create(teamPlayer)
    } catch (error) {
      return {
        success: false,
        error: `Failed to add player to team: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Remove a player from a team
   */
  async removePlayerFromTeam(teamId: string, playerId: string): Promise<ServiceResponse<void>> {
    try {
      const teamPlayer = await db.teamPlayers
        .where(['teamId', 'playerId'])
        .equals([teamId, playerId])
        .first()

      if (!teamPlayer) {
        return {
          success: false,
          error: 'Player is not on this team',
          timestamp: new Date().toISOString()
        }
      }

      return await this.delete(teamPlayer.id)
    } catch (error) {
      return {
        success: false,
        error: `Failed to remove player from team: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Update player's team-specific information
   */
  async updateTeamPlayerInfo(
    teamId: string, 
    playerId: string, 
    updates: Partial<Pick<TeamPlayer, 'jerseyNumber' | 'preferredPositions' | 'facePhotoId'>>
  ): Promise<ServiceResponse<TeamPlayer>> {
    try {
      const teamPlayer = await db.teamPlayers
        .where(['teamId', 'playerId'])
        .equals([teamId, playerId])
        .first()

      if (!teamPlayer) {
        return {
          success: false,
          error: 'Player is not on this team',
          timestamp: new Date().toISOString()
        }
      }

      // Check if jersey number is already taken (if updating jersey number)
      if (updates.jerseyNumber && updates.jerseyNumber !== teamPlayer.jerseyNumber) {
        const jerseyTaken = await db.teamPlayers
          .where(['teamId', 'jerseyNumber'])
          .equals([teamId, updates.jerseyNumber])
          .first()

        if (jerseyTaken) {
          return {
            success: false,
            error: `Jersey number ${updates.jerseyNumber} is already taken`,
            timestamp: new Date().toISOString()
          }
        }
      }

      return await this.update(teamPlayer.id, updates)
    } catch (error) {
      return {
        success: false,
        error: `Failed to update team player info: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Check if a jersey number is available for a team
   */
  async isJerseyNumberAvailable(teamId: string, jerseyNumber: string | number, excludePlayerId?: string): Promise<boolean> {
    try {
      const existing = await db.teamPlayers
        .where(['teamId', 'jerseyNumber'])
        .equals([teamId, jerseyNumber])
        .first()

      if (!existing) return true
      
      // If excluding a specific player, check if the existing assignment is for that player
      if (excludePlayerId && existing.playerId === excludePlayerId) {
        return true
      }

      return false
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const teamPlayerService = new TeamPlayerService()
