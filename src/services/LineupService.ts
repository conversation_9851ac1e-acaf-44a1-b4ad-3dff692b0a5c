import type { LineupCell, ServiceResponse } from '@/types'
import { BaseService } from './BaseService'
import { db, generateLineupCellId, parseLineupCellId } from './database'

/**
 * Service for managing LineupCell entities
 */
export class LineupService extends BaseService<LineupCell> {
  protected tableName = 'lineupCells'
  protected requiredFields: (keyof LineupCell)[] = ['id', 'gameId', 'inning', 'positionKey']

  /**
   * Get all lineup cells for a specific game
   */
  async getByGameId(gameId: string): Promise<ServiceResponse<LineupCell[]>> {
    try {
      const lineupCells = await db.lineupCells
        .where('gameId')
        .equals(gameId)
        .sortBy('inning')

      return {
        success: true,
        data: lineupCells,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get lineup cells: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get lineup cells for a specific inning
   */
  async getByInning(gameId: string, inning: number): Promise<ServiceResponse<LineupCell[]>> {
    try {
      const lineupCells = await db.lineupCells
        .where(['gameId', 'inning'])
        .equals([gameId, inning])
        .toArray()

      return {
        success: true,
        data: lineupCells,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get lineup cells for inning: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get lineup cells for a specific position across all innings
   */
  async getByPosition(gameId: string, positionKey: string): Promise<ServiceResponse<LineupCell[]>> {
    try {
      const lineupCells = await db.lineupCells
        .where(['gameId', 'positionKey'])
        .equals([gameId, positionKey])
        .sortBy('inning')

      return {
        success: true,
        data: lineupCells,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get lineup cells for position: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get all assignments for a specific player in a game
   */
  async getPlayerAssignments(gameId: string, playerId: string): Promise<ServiceResponse<LineupCell[]>> {
    try {
      const lineupCells = await db.lineupCells
        .where(['gameId', 'playerId'])
        .equals([gameId, playerId])
        .sortBy('inning')

      return {
        success: true,
        data: lineupCells,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get player assignments: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Assign a player to a specific position and inning
   */
  async assignPlayer(gameId: string, inning: number, positionKey: string, playerId: string | null): Promise<ServiceResponse<LineupCell>> {
    try {
      const cellId = generateLineupCellId(gameId, inning, positionKey)
      
      // Check if cell already exists
      const existing = await db.lineupCells.get(cellId)
      
      if (existing) {
        // Update existing cell
        const result = await this.update(cellId, { playerId })
        return result
      } else {
        // Create new cell
        const lineupCell: LineupCell = {
          id: cellId,
          gameId,
          inning,
          positionKey,
          playerId,
          createdAt: new Date().toISOString()
        }
        
        return await this.create(lineupCell)
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to assign player: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Clear a player assignment (set to null)
   */
  async clearAssignment(gameId: string, inning: number, positionKey: string): Promise<ServiceResponse<LineupCell>> {
    return await this.assignPlayer(gameId, inning, positionKey, null)
  }

  /**
   * Move a player from one position to another
   */
  async movePlayer(
    gameId: string,
    fromInning: number,
    fromPosition: string,
    toInning: number,
    toPosition: string
  ): Promise<ServiceResponse<{ from: LineupCell; to: LineupCell }>> {
    try {
      const fromCellId = generateLineupCellId(gameId, fromInning, fromPosition)
      const fromCell = await db.lineupCells.get(fromCellId)
      
      if (!fromCell || !fromCell.playerId) {
        return {
          success: false,
          error: 'No player found at source position',
          timestamp: new Date().toISOString()
        }
      }

      const playerId = fromCell.playerId

      // Clear source position
      const clearResult = await this.clearAssignment(gameId, fromInning, fromPosition)
      if (!clearResult.success) {
        return {
          success: false,
          error: `Failed to clear source position: ${clearResult.error}`,
          timestamp: new Date().toISOString()
        }
      }

      // Assign to target position
      const assignResult = await this.assignPlayer(gameId, toInning, toPosition, playerId)
      if (!assignResult.success) {
        // Rollback: restore original assignment
        await this.assignPlayer(gameId, fromInning, fromPosition, playerId)
        return {
          success: false,
          error: `Failed to assign to target position: ${assignResult.error}`,
          timestamp: new Date().toISOString()
        }
      }

      return {
        success: true,
        data: {
          from: clearResult.data!,
          to: assignResult.data!
        },
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to move player: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Copy a player to another position (duplicate assignment)
   */
  async copyPlayer(
    gameId: string,
    fromInning: number,
    fromPosition: string,
    toInning: number,
    toPosition: string
  ): Promise<ServiceResponse<LineupCell>> {
    try {
      const fromCellId = generateLineupCellId(gameId, fromInning, fromPosition)
      const fromCell = await db.lineupCells.get(fromCellId)
      
      if (!fromCell || !fromCell.playerId) {
        return {
          success: false,
          error: 'No player found at source position',
          timestamp: new Date().toISOString()
        }
      }

      return await this.assignPlayer(gameId, toInning, toPosition, fromCell.playerId)
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy player: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Clear all assignments for a game
   */
  async clearGameLineup(gameId: string): Promise<ServiceResponse<void>> {
    try {
      await db.lineupCells
        .where('gameId')
        .equals(gameId)
        .delete()

      return {
        success: true,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to clear game lineup: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Initialize empty lineup grid for a game
   */
  async initializeGameLineup(gameId: string, inningCount: number, positions: string[]): Promise<ServiceResponse<LineupCell[]>> {
    try {
      const cells: LineupCell[] = []
      
      for (let inning = 1; inning <= inningCount; inning++) {
        for (const position of positions) {
          const cellId = generateLineupCellId(gameId, inning, position)
          cells.push({
            id: cellId,
            gameId,
            inning,
            positionKey: position,
            playerId: null,
            createdAt: new Date().toISOString()
          })
        }
      }

      // Bulk insert all cells
      await db.lineupCells.bulkAdd(cells)

      return {
        success: true,
        data: cells,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to initialize game lineup: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }
}

// Export singleton instance
export const lineupService = new LineupService()
