import type { Player, ServiceResponse } from '@/types'
import { BaseService } from './BaseService'
import { db } from './database'

/**
 * Service for managing Player entities
 */
export class PlayerService extends BaseService<Player> {
  protected tableName = 'players'
  protected requiredFields: (keyof Player)[] = ['id', 'firstName', 'lastName']

  /**
   * Search players by name (fuzzy search)
   */
  async searchByName(query: string): Promise<ServiceResponse<Player[]>> {
    try {
      const searchTerm = query.toLowerCase().trim()
      
      if (!searchTerm) {
        return this.getAll()
      }

      const players = await db.players
        .filter(player => {
          const fullName = `${player.firstName} ${player.lastName}`.toLowerCase()
          const firstNameMatch = player.firstName.toLowerCase().includes(searchTerm)
          const lastNameMatch = player.lastName.toLowerCase().includes(searchTerm)
          const fullNameMatch = fullName.includes(searchTerm)
          
          return firstNameMatch || lastNameMatch || fullNameMatch
        })
        .toArray()

      return {
        success: true,
        data: players,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to search players: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get players by throwing hand
   */
  async getByThrows(throws: 'L' | 'R' | 'S'): Promise<ServiceResponse<Player[]>> {
    try {
      const players = await db.players
        .where('throws')
        .equals(throws)
        .toArray()

      return {
        success: true,
        data: players,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get players by throws: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get players by batting side
   */
  async getByBats(bats: 'L' | 'R' | 'S'): Promise<ServiceResponse<Player[]>> {
    try {
      const players = await db.players
        .where('bats')
        .equals(bats)
        .toArray()

      return {
        success: true,
        data: players,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get players by bats: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Check if a player name already exists (for duplicate prevention)
   */
  async isNameTaken(firstName: string, lastName: string, excludeId?: string): Promise<boolean> {
    try {
      const existing = await db.players
        .filter(player => 
          player.firstName.toLowerCase() === firstName.toLowerCase() &&
          player.lastName.toLowerCase() === lastName.toLowerCase() &&
          player.id !== excludeId
        )
        .first()

      return !!existing
    } catch {
      return false
    }
  }

  /**
   * Get player statistics (will be expanded in future phases)
   */
  async getPlayerStats(playerId: string): Promise<ServiceResponse<any>> {
    try {
      // Basic stats for now - will be expanded with lineup analysis
      const lineupCells = await db.lineupCells
        .where('playerId')
        .equals(playerId)
        .toArray()

      const stats = {
        totalAssignments: lineupCells.length,
        positionsPlayed: [...new Set(lineupCells.map(cell => cell.positionKey))],
        gamesPlayed: [...new Set(lineupCells.map(cell => cell.gameId))].length
      }

      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get player stats: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }
}

// Export singleton instance
export const playerService = new PlayerService()
