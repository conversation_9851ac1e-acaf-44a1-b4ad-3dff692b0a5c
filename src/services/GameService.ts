import type { Game, ServiceResponse } from '@/types'
import { BaseService } from './BaseService'
import { db } from './database'

/**
 * Service for managing Game entities
 */
export class GameService extends BaseService<Game> {
  protected tableName = 'games'
  protected requiredFields: (keyof Game)[] = ['id', 'teamId', 'date', 'opponent', 'homeAway', 'inningCount']

  /**
   * Get all games for a specific team
   */
  async getByTeamId(teamId: string): Promise<ServiceResponse<Game[]>> {
    try {
      const games = await db.games
        .where('teamId')
        .equals(teamId)
        .sortBy('date')

      return {
        success: true,
        data: games,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get team games: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get games by date range
   */
  async getByDateRange(startDate: string, endDate: string, teamId?: string): Promise<ServiceResponse<Game[]>> {
    try {
      let query = db.games
        .where('date')
        .between(startDate, endDate, true, true)

      const games = await query.toArray()
      
      // Filter by team if specified
      const filteredGames = teamId 
        ? games.filter(game => game.teamId === teamId)
        : games

      // Sort by date
      filteredGames.sort((a, b) => a.date.localeCompare(b.date))

      return {
        success: true,
        data: filteredGames,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get games by date range: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get upcoming games (from today forward)
   */
  async getUpcomingGames(teamId?: string): Promise<ServiceResponse<Game[]>> {
    try {
      const today = new Date().toISOString().split('T')[0]
      return await this.getByDateRange(today, '9999-12-31', teamId)
    } catch (error) {
      return {
        success: false,
        error: `Failed to get upcoming games: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get past games (before today)
   */
  async getPastGames(teamId?: string): Promise<ServiceResponse<Game[]>> {
    try {
      const today = new Date().toISOString().split('T')[0]
      return await this.getByDateRange('1900-01-01', today, teamId)
    } catch (error) {
      return {
        success: false,
        error: `Failed to get past games: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get games by opponent
   */
  async getByOpponent(opponent: string, teamId?: string): Promise<ServiceResponse<Game[]>> {
    try {
      const games = await db.games
        .filter(game => {
          const opponentMatch = game.opponent.toLowerCase().includes(opponent.toLowerCase())
          const teamMatch = !teamId || game.teamId === teamId
          return opponentMatch && teamMatch
        })
        .toArray()

      // Sort by date
      games.sort((a, b) => a.date.localeCompare(b.date))

      return {
        success: true,
        data: games,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get games by opponent: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get home vs away games
   */
  async getByHomeAway(homeAway: 'Home' | 'Away', teamId?: string): Promise<ServiceResponse<Game[]>> {
    try {
      let query = db.games.where('homeAway').equals(homeAway)
      
      const games = await query.toArray()
      
      // Filter by team if specified
      const filteredGames = teamId 
        ? games.filter(game => game.teamId === teamId)
        : games

      // Sort by date
      filteredGames.sort((a, b) => a.date.localeCompare(b.date))

      return {
        success: true,
        data: filteredGames,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get ${homeAway.toLowerCase()} games: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Check for scheduling conflicts
   */
  async checkSchedulingConflict(teamId: string, date: string, gameTime?: string, excludeGameId?: string): Promise<ServiceResponse<Game[]>> {
    try {
      const conflictingGames = await db.games
        .where(['teamId', 'date'])
        .equals([teamId, date])
        .filter(game => game.id !== excludeGameId)
        .toArray()

      // If no game time specified, any game on the same date is a conflict
      if (!gameTime) {
        return {
          success: true,
          data: conflictingGames,
          timestamp: new Date().toISOString()
        }
      }

      // Check for time conflicts (simplified - could be enhanced with actual time parsing)
      const timeConflicts = conflictingGames.filter(game => game.gameTime === gameTime)

      return {
        success: true,
        data: timeConflicts,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to check scheduling conflicts: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get game statistics
   */
  async getGameStats(gameId: string): Promise<ServiceResponse<any>> {
    try {
      const lineupCells = await db.lineupCells
        .where('gameId')
        .equals(gameId)
        .toArray()

      const totalCells = lineupCells.length
      const filledCells = lineupCells.filter(cell => cell.playerId).length
      const uniquePlayers = new Set(lineupCells.map(cell => cell.playerId).filter(Boolean)).size

      const stats = {
        totalLineupCells: totalCells,
        filledCells: filledCells,
        emptyCell: totalCells - filledCells,
        completionPercentage: totalCells > 0 ? Math.round((filledCells / totalCells) * 100) : 0,
        uniquePlayersUsed: uniquePlayers
      }

      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get game stats: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }
}

// Export singleton instance
export const gameService = new GameService()
