import type { Team, ServiceResponse } from '@/types'
import { BaseService } from './BaseService'
import { db } from './database'

/**
 * Service for managing Team entities
 */
export class TeamService extends BaseService<Team> {
  protected tableName = 'teams'
  protected requiredFields: (keyof Team)[] = ['id', 'name', 'season']

  /**
   * Get teams by season
   */
  async getBySeason(season: string): Promise<ServiceResponse<Team[]>> {
    try {
      const teams = await db.teams
        .where('season')
        .equals(season)
        .toArray()

      return {
        success: true,
        data: teams,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get teams by season: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get teams by head coach
   */
  async getByCoach(headCoach: string): Promise<ServiceResponse<Team[]>> {
    try {
      const teams = await db.teams
        .where('headCoach')
        .equals(headCoach)
        .toArray()

      return {
        success: true,
        data: teams,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get teams by coach: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Search teams by name
   */
  async searchByName(query: string): Promise<ServiceResponse<Team[]>> {
    try {
      const searchTerm = query.toLowerCase().trim()
      
      if (!searchTerm) {
        return this.getAll()
      }

      const teams = await db.teams
        .filter(team => 
          team.name.toLowerCase().includes(searchTerm)
        )
        .toArray()

      return {
        success: true,
        data: teams,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to search teams: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Check if a team name already exists in a season
   */
  async isNameTaken(name: string, season: string, excludeId?: string): Promise<boolean> {
    try {
      const existing = await db.teams
        .filter(team => 
          team.name.toLowerCase() === name.toLowerCase() &&
          team.season === season &&
          team.id !== excludeId
        )
        .first()

      return !!existing
    } catch {
      return false
    }
  }

  /**
   * Get team statistics
   */
  async getTeamStats(teamId: string): Promise<ServiceResponse<any>> {
    try {
      // Get team players count
      const teamPlayersCount = await db.teamPlayers
        .where('teamId')
        .equals(teamId)
        .count()

      // Get games count
      const gamesCount = await db.games
        .where('teamId')
        .equals(teamId)
        .count()

      // Get total lineup assignments
      const games = await db.games
        .where('teamId')
        .equals(teamId)
        .toArray()

      let totalLineupCells = 0
      for (const game of games) {
        const cellCount = await db.lineupCells
          .where('gameId')
          .equals(game.id)
          .count()
        totalLineupCells += cellCount
      }

      const stats = {
        playersCount: teamPlayersCount,
        gamesCount: gamesCount,
        totalLineupAssignments: totalLineupCells
      }

      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get team stats: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get all unique seasons
   */
  async getAllSeasons(): Promise<ServiceResponse<string[]>> {
    try {
      const teams = await db.teams.toArray()
      const seasons = [...new Set(teams.map(team => team.season))].sort()

      return {
        success: true,
        data: seasons,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get seasons: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }
}

// Export singleton instance
export const teamService = new TeamService()
