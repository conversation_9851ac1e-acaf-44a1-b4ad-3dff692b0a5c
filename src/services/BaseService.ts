import type { ServiceResponse, QueryOptions } from '@/types'
import { db } from './database'

/**
 * Base service class providing common CRUD operations and utilities
 */
export abstract class BaseService<T extends { id: string }> {
  protected abstract tableName: string
  protected abstract requiredFields: (keyof T)[]

  /**
   * Get the Dexie table for this service
   */
  protected get table() {
    return (db as any)[this.tableName]
  }

  /**
   * Validate an entity before saving
   */
  protected validate(entity: Partial<T>): string[] {
    const errors: string[] = []
    
    for (const field of this.requiredFields) {
      if (!entity[field]) {
        errors.push(`Missing required field: ${String(field)}`)
      }
    }
    
    return errors
  }

  /**
   * Create a new entity
   */
  async create(entity: Omit<T, 'id'> & { id?: string }): Promise<ServiceResponse<T>> {
    try {
      // Generate ID if not provided
      if (!entity.id) {
        entity.id = crypto.randomUUID()
      }

      // Validate entity
      const errors = this.validate(entity as T)
      if (errors.length > 0) {
        return {
          success: false,
          error: `Validation failed: ${errors.join(', ')}`,
          timestamp: new Date().toISOString()
        }
      }

      // Save to database
      await this.table.add(entity)
      const created = await this.table.get(entity.id)

      return {
        success: true,
        data: created,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to create entity: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get an entity by ID
   */
  async getById(id: string): Promise<ServiceResponse<T>> {
    try {
      const entity = await this.table.get(id)
      
      if (!entity) {
        return {
          success: false,
          error: `Entity with id ${id} not found`,
          timestamp: new Date().toISOString()
        }
      }

      return {
        success: true,
        data: entity,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get entity: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get all entities with optional query options
   */
  async getAll(options?: QueryOptions): Promise<ServiceResponse<T[]>> {
    try {
      let query = this.table.toCollection()

      // Apply ordering
      if (options?.orderBy) {
        query = this.table.orderBy(options.orderBy)
        if (options.orderDirection === 'desc') {
          query = query.reverse()
        }
      }

      // Apply pagination
      if (options?.offset) {
        query = query.offset(options.offset)
      }
      if (options?.limit) {
        query = query.limit(options.limit)
      }

      const entities = await query.toArray()

      return {
        success: true,
        data: entities,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get entities: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Update an entity
   */
  async update(id: string, updates: Partial<T>): Promise<ServiceResponse<T>> {
    try {
      // Check if entity exists
      const existing = await this.table.get(id)
      if (!existing) {
        return {
          success: false,
          error: `Entity with id ${id} not found`,
          timestamp: new Date().toISOString()
        }
      }

      // Merge updates with existing entity
      const updated = { ...existing, ...updates, id }

      // Validate updated entity
      const errors = this.validate(updated)
      if (errors.length > 0) {
        return {
          success: false,
          error: `Validation failed: ${errors.join(', ')}`,
          timestamp: new Date().toISOString()
        }
      }

      // Save updates
      await this.table.update(id, updates)
      const result = await this.table.get(id)

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to update entity: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Delete an entity
   */
  async delete(id: string): Promise<ServiceResponse<void>> {
    try {
      const existing = await this.table.get(id)
      if (!existing) {
        return {
          success: false,
          error: `Entity with id ${id} not found`,
          timestamp: new Date().toISOString()
        }
      }

      await this.table.delete(id)

      return {
        success: true,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to delete entity: ${error}`,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Check if an entity exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const entity = await this.table.get(id)
      return !!entity
    } catch {
      return false
    }
  }

  /**
   * Count total entities
   */
  async count(): Promise<number> {
    try {
      return await this.table.count()
    } catch {
      return 0
    }
  }
}
