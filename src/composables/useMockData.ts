import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { Team, Player, Game } from '@/types'

// Mock data extracted from App.vue for experimentation and development
export const useMockData = () => {
  const route = useRoute()
  const router = useRouter()
  const teams = ref<Team[]>([
    {
      id: '1',
      name: 'Wildcats 12U',
      season: 'Fall 2025',
      headCoach: '<PERSON>',
      players: [
        { id: '1-1', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'R', bats: 'R' },
        { id: '1-2', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'L', bats: 'L' },
        { id: '1-3', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'R', bats: 'S' },
        { id: '1-4', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'R', bats: 'R' },
        { id: '1-5', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'L', bats: 'R' },
        { id: '1-6', firstName: '<PERSON>', lastName: '<PERSON>', throws: '<PERSON>', bats: 'L' },
        { id: '1-7', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'R', bats: 'R' },
        { id: '1-8', firstName: 'Hunter', lastName: '<PERSON>', throws: 'R', bats: 'R' },
        { id: '1-9', firstName: 'Ian', lastName: '<PERSON>', throws: 'L', bats: 'S' }
      ],
      games: [
        {
          id: '1-g1',
          teamId: '1',
          date: '2025-09-15',
          gameTime: '6:00 PM',
          arrivalTime: '5:30 PM',
          opponent: 'Lions 12U',
          homeAway: 'Home',
          address: '123 Baseball Field Rd, Hometown, PA',
          notes: 'Season opener - full lineup expected',
          inningCount: 12
        },
        {
          id: '1-g2',
          teamId: '1',
          date: '2025-09-22',
          gameTime: '4:00 PM',
          arrivalTime: '3:30 PM',
          opponent: 'Panthers 12U',
          homeAway: 'Away',
          address: '456 Sports Complex Dr, Visiting Town, PA',
          inningCount: 12
        }
      ]
    },
    {
      id: '2',
      name: 'Eagles 13U',
      season: 'Fall 2025',
      headCoach: 'Coach Smith',
      players: [
        { id: '2-1', firstName: 'Aaron', lastName: 'Jackson', throws: 'R', bats: 'R' },
        { id: '2-2', firstName: 'Blake', lastName: 'White', throws: 'R', bats: 'L' },
        { id: '2-3', firstName: 'Connor', lastName: 'Taylor', throws: 'L', bats: 'L' },
        { id: '2-4', firstName: 'Derek', lastName: 'Thomas', throws: 'R', bats: 'R' },
        { id: '2-5', firstName: 'Evan', lastName: 'Harris', throws: 'R', bats: 'S' },
        { id: '2-6', firstName: 'Finn', lastName: 'Clark', throws: 'L', bats: 'R' },
        { id: '2-7', firstName: 'Gavin', lastName: 'Lewis', throws: 'R', bats: 'R' },
        { id: '2-8', firstName: 'Henry', lastName: 'Walker', throws: 'R', bats: 'L' },
        { id: '2-9', firstName: 'Isaac', lastName: 'Hall', throws: 'L', bats: 'L' },
        { id: '2-10', firstName: 'Jake', lastName: 'Allen', throws: 'R', bats: 'R' },
        { id: '2-11', firstName: 'Kyle', lastName: 'Young', throws: 'R', bats: 'S' },
        { id: '2-12', firstName: 'Logan', lastName: 'King', throws: 'L', bats: 'R' }
      ],
      games: [
        {
          id: '2-g1',
          teamId: '2',
          date: '2025-09-18',
          gameTime: '7:00 PM',
          arrivalTime: '6:30 PM',
          opponent: 'Hawks 13U',
          homeAway: 'Home',
          address: '789 Diamond Ave, Eagle City, PA',
          notes: 'Night game - bring lights',
          inningCount: 14
        },
        {
          id: '2-g2',
          teamId: '2',
          date: '2025-09-25',
          gameTime: '5:30 PM',
          arrivalTime: '5:00 PM',
          opponent: 'Wolves 13U',
          homeAway: 'Away',
          address: '321 Field St, Wolf Town, PA',
          inningCount: 14
        },
        {
          id: '2-g3',
          teamId: '2',
          date: '2025-10-02',
          gameTime: '3:00 PM',
          arrivalTime: '2:30 PM',
          opponent: 'Bears 13U',
          homeAway: 'Home',
          address: '789 Diamond Ave, Eagle City, PA',
          inningCount: 14
        }
      ]
    },
    {
      id: '3',
      name: 'Tigers 14U',
      season: 'Fall 2025',
      headCoach: 'Coach Williams',
      players: [
        { id: '3-1', firstName: 'Adrian', lastName: 'Wright', throws: 'R', bats: 'R' },
        { id: '3-2', firstName: 'Brady', lastName: 'Lopez', throws: 'L', bats: 'L' },
        { id: '3-3', firstName: 'Caleb', lastName: 'Hill', throws: 'R', bats: 'R' },
        { id: '3-4', firstName: 'Daniel', lastName: 'Scott', throws: 'R', bats: 'S' },
        { id: '3-5', firstName: 'Eli', lastName: 'Green', throws: 'L', bats: 'L' },
        { id: '3-6', firstName: 'Frank', lastName: 'Adams', throws: 'R', bats: 'R' },
        { id: '3-7', firstName: 'George', lastName: 'Baker', throws: 'R', bats: 'L' },
        { id: '3-8', firstName: 'Henry', lastName: 'Gonzalez', throws: 'L', bats: 'R' },
        { id: '3-9', firstName: 'Ivan', lastName: 'Nelson', throws: 'R', bats: 'R' },
        { id: '3-10', firstName: 'Jack', lastName: 'Carter', throws: 'R', bats: 'S' },
        { id: '3-11', firstName: 'Kevin', lastName: 'Mitchell', throws: 'L', bats: 'L' },
        { id: '3-12', firstName: 'Liam', lastName: 'Perez', throws: 'R', bats: 'R' },
        { id: '3-13', firstName: 'Mason', lastName: 'Roberts', throws: 'R', bats: 'L' },
        { id: '3-14', firstName: 'Noah', lastName: 'Turner', throws: 'L', bats: 'S' },
        { id: '3-15', firstName: 'Owen', lastName: 'Phillips', throws: 'R', bats: 'R' }
      ],
      games: [
        {
          id: '3-g1',
          teamId: '3',
          date: '2025-09-20',
          gameTime: '6:30 PM',
          arrivalTime: '6:00 PM',
          opponent: 'Sharks 14U',
          homeAway: 'Away',
          address: '654 Stadium Blvd, Shark City, PA',
          notes: 'Championship qualifier game',
          inningCount: 16
        },
        {
          id: '3-g2',
          teamId: '3',
          date: '2025-09-27',
          gameTime: '4:30 PM',
          arrivalTime: '4:00 PM',
          opponent: 'Cardinals 14U',
          homeAway: 'Home',
          address: '987 Tiger Field Way, Tiger Town, PA',
          inningCount: 16
        },
        {
          id: '3-g3',
          teamId: '3',
          date: '2025-10-04',
          gameTime: '1:00 PM',
          arrivalTime: '12:30 PM',
          opponent: 'Bulldogs 14U',
          homeAway: 'Away',
          address: '159 Bulldog Park Dr, Dog City, PA',
          notes: 'Afternoon game - early arrival',
          inningCount: 16
        },
        {
          id: '3-g4',
          teamId: '3',
          date: '2025-10-11',
          gameTime: '7:30 PM',
          arrivalTime: '7:00 PM',
          opponent: 'Mustangs 14U',
          homeAway: 'Home',
          address: '987 Tiger Field Way, Tiger Town, PA',
          notes: 'Senior night game',
          inningCount: 16
        }
      ]
    }
  ])

  // Computed values for easy access
  const selectedTeam = ref<Team | null>(null)
  const selectedGame = ref<Game | null>(null)

  // Navigation state
  const navigationState = computed(() => {
    if (selectedGame.value) return 'lineup'
    if (selectedTeam.value) return 'games'
    return 'teams'
  })

  // URL synchronization helper
  const updateUrl = (teamId?: string, gameId?: string) => {
    const query: Record<string, string> = {}
    if (teamId) query.teamId = teamId
    if (gameId) query.gameId = gameId

    router.replace({
      name: route.name || undefined,
      query
    })
  }

  // Methods for team/game selection with URL sync
  const selectTeam = (team: Team) => {
    selectedTeam.value = team
    selectedGame.value = null
    updateUrl(team.id)
  }

  const selectGame = (game: Game) => {
    selectedGame.value = game
    updateUrl(selectedTeam.value?.id, game.id)
  }

  const goBackToTeams = () => {
    selectedTeam.value = null
    selectedGame.value = null
    updateUrl()
  }

  const goBackToGames = () => {
    selectedGame.value = null
    updateUrl(selectedTeam.value?.id)
  }

  // Initialize from URL parameters
  const initializeFromUrl = () => {
    const teamId = route.query.teamId as string
    const gameId = route.query.gameId as string

    if (teamId) {
      const team = teams.value.find(t => t.id === teamId)
      if (team) {
        selectedTeam.value = team

        if (gameId) {
          const game = team.games.find(g => g.id === gameId)
          if (game) {
            selectedGame.value = game
          }
        }
      }
    }
  }

  // Watch for route changes to sync state
  watch(() => route.query, (newQuery) => {
    const teamId = newQuery.teamId as string
    const gameId = newQuery.gameId as string

    if (!teamId) {
      selectedTeam.value = null
      selectedGame.value = null
      return
    }

    const team = teams.value.find(t => t.id === teamId)
    if (team && selectedTeam.value?.id !== team.id) {
      selectedTeam.value = team
    }

    if (!gameId) {
      selectedGame.value = null
      return
    }

    if (team) {
      const game = team.games.find(g => g.id === gameId)
      if (game && selectedGame.value?.id !== game.id) {
        selectedGame.value = game
      }
    }
  }, { immediate: true })

  // Initialize on mount
  onMounted(() => {
    initializeFromUrl()
  })

  return {
    teams,
    selectedTeam,
    selectedGame,
    navigationState,
    selectTeam,
    selectGame,
    goBackToTeams,
    goBackToGames
  }
}