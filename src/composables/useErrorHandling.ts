import { ref, computed } from 'vue'
import type { Ref } from 'vue'

/**
 * Error handling composable with retry logic and user-friendly messages
 */
export function useErrorHandling(options: {
  maxRetries?: number
  retryDelayMs?: number
  showUserFriendlyMessages?: boolean
} = {}) {
  const {
    maxRetries = 3,
    retryDelayMs = 1000,
    showUserFriendlyMessages = true
  } = options

  const errors = ref<Array<{
    id: string
    message: string
    timestamp: Date
    type: 'error' | 'warning' | 'info'
    retryable: boolean
    retryCount: number
  }>>([])

  const hasErrors = computed(() => errors.value.length > 0)
  const latestError = computed(() => errors.value[errors.value.length - 1])

  /**
   * Convert technical error to user-friendly message
   */
  const getUserFriendlyMessage = (error: string): string => {
    if (!showUserFriendlyMessages) {
      return error
    }

    // Common error patterns and their user-friendly messages
    const errorMappings: Record<string, string> = {
      'Network request failed': 'Unable to connect to the server. Please check your internet connection.',
      'Failed to fetch': 'Unable to connect to the server. Please check your internet connection.',
      'Timeout': 'The request took too long. Please try again.',
      'Not found': 'The requested item could not be found.',
      'Unauthorized': 'You do not have permission to perform this action.',
      'Forbidden': 'Access to this resource is forbidden.',
      'Internal server error': 'A server error occurred. Please try again later.',
      'Bad request': 'The request was invalid. Please check your input.',
      'Conflict': 'This action conflicts with existing data.',
      'Validation failed': 'Please check your input and try again.',
      'Database error': 'A database error occurred. Please try again.',
      'KeyPath': 'Database configuration error. Please refresh the page.'
    }

    // Find matching error pattern
    for (const [pattern, message] of Object.entries(errorMappings)) {
      if (error.toLowerCase().includes(pattern.toLowerCase())) {
        return message
      }
    }

    // Default fallback
    return 'An unexpected error occurred. Please try again.'
  }

  /**
   * Add an error
   */
  const addError = (
    message: string, 
    type: 'error' | 'warning' | 'info' = 'error',
    retryable: boolean = true
  ) => {
    const error = {
      id: crypto.randomUUID(),
      message: getUserFriendlyMessage(message),
      timestamp: new Date(),
      type,
      retryable,
      retryCount: 0
    }

    errors.value.push(error)

    // Auto-remove info messages after 5 seconds
    if (type === 'info') {
      setTimeout(() => {
        removeError(error.id)
      }, 5000)
    }

    return error.id
  }

  /**
   * Remove an error
   */
  const removeError = (id: string) => {
    const index = errors.value.findIndex(error => error.id === id)
    if (index !== -1) {
      errors.value.splice(index, 1)
    }
  }

  /**
   * Clear all errors
   */
  const clearErrors = () => {
    errors.value = []
  }

  /**
   * Clear errors of a specific type
   */
  const clearErrorsByType = (type: 'error' | 'warning' | 'info') => {
    errors.value = errors.value.filter(error => error.type !== type)
  }

  /**
   * Execute function with automatic error handling and retry logic
   */
  const withErrorHandling = async <T>(
    operation: () => Promise<T>,
    operationName: string = 'Operation',
    customRetryable: boolean = true
  ): Promise<T | null> => {
    let lastError: string = ''
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = String(error)
        
        if (attempt < maxRetries && customRetryable) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelayMs * (attempt + 1)))
          continue
        }
      }
    }

    // All retries failed
    addError(`${operationName} failed: ${lastError}`, 'error', customRetryable)
    return null
  }

  /**
   * Retry a specific error
   */
  const retryError = async (errorId: string, retryFunction: () => Promise<void>) => {
    const error = errors.value.find(e => e.id === errorId)
    if (!error || !error.retryable) {
      return
    }

    if (error.retryCount >= maxRetries) {
      addError('Maximum retry attempts reached', 'error', false)
      return
    }

    try {
      error.retryCount++
      await retryFunction()
      removeError(errorId)
      addError('Operation completed successfully', 'info', false)
    } catch (err) {
      addError(`Retry failed: ${err}`, 'error', error.retryCount < maxRetries)
    }
  }

  return {
    errors,
    hasErrors,
    latestError,
    addError,
    removeError,
    clearErrors,
    clearErrorsByType,
    withErrorHandling,
    retryError
  }
}

/**
 * Loading state composable with multiple concurrent operations
 */
export function useLoadingState() {
  const loadingOperations = ref<Map<string, {
    name: string
    startTime: Date
    progress?: number
  }>>(new Map())

  const isLoading = computed(() => loadingOperations.value.size > 0)
  const loadingCount = computed(() => loadingOperations.value.size)
  const currentOperations = computed(() => Array.from(loadingOperations.value.values()))

  /**
   * Start a loading operation
   */
  const startLoading = (operationId: string, operationName: string = 'Loading...') => {
    loadingOperations.value.set(operationId, {
      name: operationName,
      startTime: new Date()
    })
  }

  /**
   * Update loading progress
   */
  const updateProgress = (operationId: string, progress: number) => {
    const operation = loadingOperations.value.get(operationId)
    if (operation) {
      operation.progress = Math.max(0, Math.min(100, progress))
    }
  }

  /**
   * Stop a loading operation
   */
  const stopLoading = (operationId: string) => {
    loadingOperations.value.delete(operationId)
  }

  /**
   * Clear all loading operations
   */
  const clearLoading = () => {
    loadingOperations.value.clear()
  }

  /**
   * Execute function with automatic loading state management
   */
  const withLoading = async <T>(
    operation: () => Promise<T>,
    operationName: string = 'Loading...',
    operationId?: string
  ): Promise<T> => {
    const id = operationId || crypto.randomUUID()
    
    startLoading(id, operationName)
    
    try {
      const result = await operation()
      return result
    } finally {
      stopLoading(id)
    }
  }

  return {
    loadingOperations,
    isLoading,
    loadingCount,
    currentOperations,
    startLoading,
    updateProgress,
    stopLoading,
    clearLoading,
    withLoading
  }
}

/**
 * Toast notification composable for user feedback
 */
export function useToastNotifications() {
  const notifications = ref<Array<{
    id: string
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
    timestamp: Date
    duration: number
    persistent: boolean
  }>>([])

  /**
   * Show a notification
   */
  const showNotification = (
    message: string,
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    duration: number = 5000,
    persistent: boolean = false
  ) => {
    const notification = {
      id: crypto.randomUUID(),
      message,
      type,
      timestamp: new Date(),
      duration,
      persistent
    }

    notifications.value.push(notification)

    // Auto-remove non-persistent notifications
    if (!persistent) {
      setTimeout(() => {
        removeNotification(notification.id)
      }, duration)
    }

    return notification.id
  }

  /**
   * Remove a notification
   */
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  /**
   * Clear all notifications
   */
  const clearNotifications = () => {
    notifications.value = []
  }

  /**
   * Show success notification
   */
  const showSuccess = (message: string, duration?: number) => {
    return showNotification(message, 'success', duration)
  }

  /**
   * Show error notification
   */
  const showError = (message: string, persistent: boolean = false) => {
    return showNotification(message, 'error', persistent ? 0 : 8000, persistent)
  }

  /**
   * Show warning notification
   */
  const showWarning = (message: string, duration?: number) => {
    return showNotification(message, 'warning', duration)
  }

  /**
   * Show info notification
   */
  const showInfo = (message: string, duration?: number) => {
    return showNotification(message, 'info', duration)
  }

  return {
    notifications,
    showNotification,
    removeNotification,
    clearNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}
