import { computed, onMounted } from 'vue'
import { useTeamStore } from '@/stores/useTeamStore'
import { useGameStore } from '@/stores/useGameStore'
import { useLineupStore } from '@/stores/useLineupStore'
import { useErrorHandling, useLoadingState, useToastNotifications } from '@/composables/useErrorHandling'
import { downloadJSONBackup, downloadLineupCSV, downloadTeamRosterCSV, selectAndImportJSON } from '@/utils/dataExport'
import type { TeamWithRelations } from '@/types'

/**
 * Backward compatibility composable that provides the same interface as useMockData
 * but uses persistent storage instead of hardcoded data
 */
export function usePersistentData() {
  // ============================================================================
  // STORES
  // ============================================================================
  
  const teamStore = useTeamStore()
  const gameStore = useGameStore()
  const lineupStore = useLineupStore()

  // ============================================================================
  // ERROR HANDLING & LOADING
  // ============================================================================
  
  const { withErrorHandling, addError } = useErrorHandling()
  const { withLoading, isLoading } = useLoadingState()
  const { showSuccess, showError, showInfo } = useToastNotifications()

  // ============================================================================
  // COMPUTED PROPERTIES (BACKWARD COMPATIBILITY)
  // ============================================================================

  /**
   * Teams with relations (same interface as useMockData)
   */
  const teams = computed((): TeamWithRelations[] => {
    return teamStore.teamsWithRelations.map(team => ({
      ...team,
      games: gameStore.getGamesByTeamId(team.id)
    }))
  })

  /**
   * Loading state
   */
  const loading = computed(() => {
    return teamStore.loading || gameStore.loading || lineupStore.loading || isLoading.value
  })

  /**
   * Error state
   */
  const error = computed(() => {
    return teamStore.error || gameStore.error || lineupStore.error
  })

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  /**
   * Initialize all data stores
   */
  const initializeData = async () => {
    await withLoading(async () => {
      const results = await withErrorHandling(async () => {
        // Load all data in parallel
        const [teamsResult, gamesResult] = await Promise.all([
          teamStore.loadTeams(),
          gameStore.loadGames()
        ])

        if (!teamsResult.success) {
          throw new Error(teamsResult.error || 'Failed to load teams')
        }
        if (!gamesResult.success) {
          throw new Error(gamesResult.error || 'Failed to load games')
        }

        return { teams: teamsResult.data, games: gamesResult.data }
      }, 'Loading application data')

      if (results) {
        showInfo(`Loaded ${results.teams?.length || 0} teams and ${results.games?.length || 0} games`)
      }
    }, 'Initializing application')
  }

  // ============================================================================
  // TEAM OPERATIONS
  // ============================================================================

  /**
   * Get team by ID (backward compatibility)
   */
  const getTeamById = (id: string): TeamWithRelations | undefined => {
    const team = teamStore.getTeamWithRelationsById(id)
    if (team) {
      return {
        ...team,
        games: gameStore.getGamesByTeamId(id)
      }
    }
    return undefined
  }

  /**
   * Create a new team
   */
  const createTeam = async (teamData: Omit<TeamWithRelations, 'id' | 'players' | 'games'>) => {
    return await withErrorHandling(async () => {
      const result = await teamStore.createTeam(teamData)
      if (result.success) {
        showSuccess(`Team "${teamData.name}" created successfully`)
      }
      return result
    }, 'Creating team')
  }

  /**
   * Update a team
   */
  const updateTeam = async (id: string, updates: Partial<TeamWithRelations>) => {
    return await withErrorHandling(async () => {
      const result = await teamStore.updateTeam(id, updates)
      if (result.success) {
        showSuccess('Team updated successfully')
      }
      return result
    }, 'Updating team')
  }

  /**
   * Delete a team
   */
  const deleteTeam = async (id: string) => {
    return await withErrorHandling(async () => {
      const team = teamStore.getTeamById(id)
      const result = await teamStore.deleteTeam(id)
      if (result.success) {
        showSuccess(`Team "${team?.name}" deleted successfully`)
      }
      return result
    }, 'Deleting team')
  }

  // ============================================================================
  // GAME OPERATIONS
  // ============================================================================

  /**
   * Create a new game
   */
  const createGame = async (gameData: any) => {
    return await withErrorHandling(async () => {
      const result = await gameStore.createGame(gameData)
      if (result.success) {
        showSuccess(`Game vs ${gameData.opponent} created successfully`)
      }
      return result
    }, 'Creating game')
  }

  /**
   * Update a game
   */
  const updateGame = async (id: string, updates: any) => {
    return await withErrorHandling(async () => {
      const result = await gameStore.updateGame(id, updates)
      if (result.success) {
        showSuccess('Game updated successfully')
      }
      return result
    }, 'Updating game')
  }

  /**
   * Delete a game
   */
  const deleteGame = async (id: string) => {
    return await withErrorHandling(async () => {
      const game = gameStore.getGameById(id)
      const result = await gameStore.deleteGame(id)
      if (result.success) {
        showSuccess(`Game vs ${game?.opponent} deleted successfully`)
      }
      return result
    }, 'Deleting game')
  }

  // ============================================================================
  // LINEUP OPERATIONS
  // ============================================================================

  /**
   * Load lineup for a game
   */
  const loadGameLineup = async (gameId: string) => {
    return await withErrorHandling(async () => {
      const result = await lineupStore.loadGameLineup(gameId)
      return result
    }, 'Loading game lineup')
  }

  /**
   * Assign player to position
   */
  const assignPlayer = async (gameId: string, inning: number, positionKey: string, playerId: string | null) => {
    return await withErrorHandling(async () => {
      const result = await lineupStore.assignPlayer(gameId, inning, positionKey, playerId)
      return result
    }, 'Assigning player', false) // Don't show error notifications for lineup changes
  }

  /**
   * Initialize empty lineup for a game
   */
  const initializeGameLineup = async (gameId: string, inningCount: number, positions: string[]) => {
    return await withErrorHandling(async () => {
      const result = await lineupStore.initializeGameLineup(gameId, inningCount, positions)
      if (result.success) {
        showInfo('Lineup grid initialized')
      }
      return result
    }, 'Initializing lineup')
  }

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  /**
   * Refresh all data
   */
  const refreshData = async () => {
    await withLoading(async () => {
      teamStore.clearData()
      gameStore.clearData()
      lineupStore.clearData()
      await initializeData()
    }, 'Refreshing data')
  }

  /**
   * Export data for backup (JSON)
   */
  const exportData = async () => {
    return await withErrorHandling(async () => {
      const result = await downloadJSONBackup()
      if (result.success) {
        showSuccess('Data exported successfully')
      }
      return result
    }, 'Exporting data')
  }

  /**
   * Import data from backup (JSON)
   */
  const importData = async () => {
    return await withErrorHandling(async () => {
      const result = await selectAndImportJSON()
      if (result.success && result.statistics) {
        const stats = result.statistics
        showSuccess(
          `Data imported successfully! ` +
          `Teams: ${stats.teamsImported}, Players: ${stats.playersImported}, ` +
          `Games: ${stats.gamesImported}, Lineup Cells: ${stats.lineupCellsImported}`
        )
        // Refresh data after import
        await refreshData()
      }
      return result
    }, 'Importing data')
  }

  /**
   * Export game lineup as CSV
   */
  const exportGameLineup = async (gameId: string) => {
    return await withErrorHandling(async () => {
      const result = await downloadLineupCSV(gameId)
      if (result.success) {
        showSuccess('Lineup exported successfully')
      }
      return result
    }, 'Exporting lineup')
  }

  /**
   * Export team roster as CSV
   */
  const exportTeamRoster = async (teamId: string) => {
    return await withErrorHandling(async () => {
      const result = await downloadTeamRosterCSV(teamId)
      if (result.success) {
        showSuccess('Roster exported successfully')
      }
      return result
    }, 'Exporting roster')
  }

  /**
   * Get application statistics
   */
  const getStats = computed(() => {
    return {
      totalTeams: teamStore.teams.length,
      totalPlayers: teamStore.players.length,
      totalGames: gameStore.games.length,
      totalLineupCells: lineupStore.lineupCells.length,
      seasons: teamStore.allSeasons
    }
  })

  // ============================================================================
  // LIFECYCLE
  // ============================================================================

  onMounted(() => {
    initializeData()
  })

  // ============================================================================
  // RETURN INTERFACE (BACKWARD COMPATIBILITY)
  // ============================================================================

  return {
    // Data (same interface as useMockData)
    teams,
    loading,
    error,

    // Team operations
    getTeamById,
    createTeam,
    updateTeam,
    deleteTeam,

    // Game operations
    createGame,
    updateGame,
    deleteGame,

    // Lineup operations
    loadGameLineup,
    assignPlayer,
    initializeGameLineup,

    // Utility functions
    refreshData,
    exportData,
    importData,
    exportGameLineup,
    exportTeamRoster,
    getStats,

    // Store access (for advanced usage)
    teamStore,
    gameStore,
    lineupStore
  }
}
