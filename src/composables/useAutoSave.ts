import { ref, watch, nextTick } from 'vue'
import type { Ref } from 'vue'

/**
 * Auto-save composable with debouncing and error handling
 */
export function useAutoSave<T>(
  data: Ref<T>,
  saveFunction: (data: T) => Promise<{ success: boolean; error?: string }>,
  options: {
    debounceMs?: number
    enabled?: Ref<boolean>
    onSaveStart?: () => void
    onSaveSuccess?: () => void
    onSaveError?: (error: string) => void
  } = {}
) {
  const {
    debounceMs = 1000,
    enabled = ref(true),
    onSaveStart,
    onSaveSuccess,
    onSaveError
  } = options

  const isSaving = ref(false)
  const lastSaved = ref<Date | null>(null)
  const saveError = ref<string | null>(null)
  const pendingChanges = ref(false)

  let saveTimeout: NodeJS.Timeout | null = null
  let lastSavedData: T | null = null

  /**
   * Debounced save function
   */
  const debouncedSave = async () => {
    if (!enabled.value || isSaving.value) {
      return
    }

    // Check if data actually changed
    if (lastSavedData && JSON.stringify(data.value) === JSON.stringify(lastSavedData)) {
      pendingChanges.value = false
      return
    }

    isSaving.value = true
    saveError.value = null
    pendingChanges.value = false
    onSaveStart?.()

    try {
      const result = await saveFunction(data.value)
      
      if (result.success) {
        lastSaved.value = new Date()
        lastSavedData = JSON.parse(JSON.stringify(data.value)) // Deep clone
        onSaveSuccess?.()
      } else {
        saveError.value = result.error || 'Save failed'
        pendingChanges.value = true // Mark as having unsaved changes
        onSaveError?.(saveError.value)
      }
    } catch (error) {
      saveError.value = String(error)
      pendingChanges.value = true
      onSaveError?.(saveError.value)
    } finally {
      isSaving.value = false
    }
  }

  /**
   * Schedule a save operation
   */
  const scheduleSave = () => {
    if (!enabled.value) {
      return
    }

    pendingChanges.value = true

    if (saveTimeout) {
      clearTimeout(saveTimeout)
    }

    saveTimeout = setTimeout(debouncedSave, debounceMs)
  }

  /**
   * Force immediate save
   */
  const saveNow = async () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout)
      saveTimeout = null
    }
    await debouncedSave()
  }

  /**
   * Cancel pending save
   */
  const cancelSave = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout)
      saveTimeout = null
    }
    pendingChanges.value = false
  }

  // Watch for data changes
  watch(
    data,
    () => {
      if (enabled.value) {
        scheduleSave()
      }
    },
    { deep: true }
  )

  // Watch for enabled state changes
  watch(enabled, (newEnabled) => {
    if (newEnabled && pendingChanges.value) {
      scheduleSave()
    } else if (!newEnabled) {
      cancelSave()
    }
  })

  return {
    isSaving,
    lastSaved,
    saveError,
    pendingChanges,
    saveNow,
    cancelSave
  }
}

/**
 * Optimistic update composable for immediate UI feedback
 */
export function useOptimisticUpdate<T, R>(
  updateFunction: (data: T) => Promise<{ success: boolean; data?: R; error?: string }>,
  options: {
    onOptimisticUpdate?: (data: T) => void
    onSuccess?: (result: R) => void
    onError?: (error: string, originalData: T) => void
    onRollback?: (originalData: T) => void
  } = {}
) {
  const {
    onOptimisticUpdate,
    onSuccess,
    onError,
    onRollback
  } = options

  const isUpdating = ref(false)
  const updateError = ref<string | null>(null)

  /**
   * Perform optimistic update
   */
  const performUpdate = async (data: T, rollbackData?: T) => {
    if (isUpdating.value) {
      return { success: false, error: 'Update already in progress' }
    }

    isUpdating.value = true
    updateError.value = null

    // Apply optimistic update immediately
    onOptimisticUpdate?.(data)

    try {
      const result = await updateFunction(data)
      
      if (result.success) {
        onSuccess?.(result.data!)
        return result
      } else {
        // Rollback on failure
        if (rollbackData) {
          onRollback?.(rollbackData)
        }
        updateError.value = result.error || 'Update failed'
        onError?.(updateError.value, rollbackData || data)
        return result
      }
    } catch (error) {
      // Rollback on exception
      if (rollbackData) {
        onRollback?.(rollbackData)
      }
      updateError.value = String(error)
      onError?.(updateError.value, rollbackData || data)
      return { success: false, error: updateError.value }
    } finally {
      isUpdating.value = false
    }
  }

  return {
    isUpdating,
    updateError,
    performUpdate
  }
}

/**
 * Batch operations composable for multiple related updates
 */
export function useBatchOperations<T>(
  batchFunction: (operations: T[]) => Promise<{ success: boolean; results?: any[]; error?: string }>,
  options: {
    maxBatchSize?: number
    batchDelayMs?: number
    onBatchStart?: (operations: T[]) => void
    onBatchSuccess?: (results: any[]) => void
    onBatchError?: (error: string, operations: T[]) => void
  } = {}
) {
  const {
    maxBatchSize = 10,
    batchDelayMs = 100,
    onBatchStart,
    onBatchSuccess,
    onBatchError
  } = options

  const pendingOperations = ref<T[]>([])
  const isProcessing = ref(false)
  const batchError = ref<string | null>(null)

  let batchTimeout: NodeJS.Timeout | null = null

  /**
   * Process pending batch
   */
  const processBatch = async () => {
    if (pendingOperations.value.length === 0 || isProcessing.value) {
      return
    }

    const operations = pendingOperations.value.splice(0, maxBatchSize)
    isProcessing.value = true
    batchError.value = null
    onBatchStart?.(operations)

    try {
      const result = await batchFunction(operations)
      
      if (result.success) {
        onBatchSuccess?.(result.results || [])
      } else {
        batchError.value = result.error || 'Batch operation failed'
        onBatchError?.(batchError.value, operations)
      }
    } catch (error) {
      batchError.value = String(error)
      onBatchError?.(batchError.value, operations)
    } finally {
      isProcessing.value = false
      
      // Process remaining operations if any
      if (pendingOperations.value.length > 0) {
        scheduleBatch()
      }
    }
  }

  /**
   * Schedule batch processing
   */
  const scheduleBatch = () => {
    if (batchTimeout) {
      clearTimeout(batchTimeout)
    }

    batchTimeout = setTimeout(processBatch, batchDelayMs)
  }

  /**
   * Add operation to batch
   */
  const addOperation = (operation: T) => {
    pendingOperations.value.push(operation)
    
    if (pendingOperations.value.length >= maxBatchSize) {
      // Process immediately if batch is full
      if (batchTimeout) {
        clearTimeout(batchTimeout)
        batchTimeout = null
      }
      nextTick(processBatch)
    } else {
      scheduleBatch()
    }
  }

  /**
   * Force immediate batch processing
   */
  const processBatchNow = async () => {
    if (batchTimeout) {
      clearTimeout(batchTimeout)
      batchTimeout = null
    }
    await processBatch()
  }

  return {
    pendingOperations,
    isProcessing,
    batchError,
    addOperation,
    processBatchNow
  }
}
