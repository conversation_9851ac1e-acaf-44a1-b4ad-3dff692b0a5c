import type { 
  Team, 
  Player, 
  TeamPlayer, 
  Game, 
  TeamWithRelations,
  DataMigrationResult 
} from '@/types'

/**
 * Utilities for transforming data between denormalized (UI) and normalized (database) formats
 */

// ============================================================================
// DENORMALIZED TO NORMALIZED TRANSFORMATION
// ============================================================================

/**
 * Extract unique players from denormalized teams data
 */
export function extractPlayersFromTeams(teams: TeamWithRelations[]): Player[] {
  const playerMap = new Map<string, Player>()
  
  for (const team of teams) {
    for (const player of team.players) {
      // Use a combination of firstName + lastName as the key to detect duplicates
      const key = `${player.firstName.toLowerCase()}_${player.lastName.toLowerCase()}`
      
      if (!playerMap.has(key)) {
        // Create a global player ID if not already set
        const globalPlayer: Player = {
          ...player,
          id: player.id || crypto.randomUUID()
        }
        playerMap.set(key, globalPlayer)
      }
    }
  }
  
  return Array.from(playerMap.values())
}

/**
 * Extract teams without embedded players/games
 */
export function extractTeamsFromDenormalized(teams: TeamWithRelations[]): Team[] {
  return teams.map(team => ({
    id: team.id,
    name: team.name,
    season: team.season,
    headCoach: team.headCoach,
    assistantCoaches: team.assistantCoaches,
    notes: team.notes
  }))
}

/**
 * Create TeamPlayer relationships from denormalized data
 */
export function createTeamPlayerRelationships(
  teams: TeamWithRelations[], 
  globalPlayers: Player[]
): TeamPlayer[] {
  const teamPlayers: TeamPlayer[] = []
  const playerNameMap = new Map<string, Player>()
  
  // Create a map for quick player lookup by name
  for (const player of globalPlayers) {
    const key = `${player.firstName.toLowerCase()}_${player.lastName.toLowerCase()}`
    playerNameMap.set(key, player)
  }
  
  for (const team of teams) {
    for (let i = 0; i < team.players.length; i++) {
      const player = team.players[i]
      const key = `${player.firstName.toLowerCase()}_${player.lastName.toLowerCase()}`
      const globalPlayer = playerNameMap.get(key)
      
      if (globalPlayer) {
        teamPlayers.push({
          id: crypto.randomUUID(),
          teamId: team.id,
          playerId: globalPlayer.id,
          jerseyNumber: (i + 1).toString(), // Auto-assign jersey numbers 1, 2, 3...
          preferredPositions: [] // Will be populated later or from additional data
        })
      }
    }
  }
  
  return teamPlayers
}

/**
 * Extract games from denormalized teams
 */
export function extractGamesFromTeams(teams: TeamWithRelations[]): Game[] {
  const games: Game[] = []
  
  for (const team of teams) {
    for (const game of team.games) {
      games.push({
        ...game,
        teamId: team.id
      })
    }
  }
  
  return games
}

// ============================================================================
// NORMALIZED TO DENORMALIZED TRANSFORMATION
// ============================================================================

/**
 * Reconstruct denormalized teams from normalized database entities
 */
export async function reconstructTeamsWithRelations(
  teams: Team[],
  players: Player[],
  teamPlayers: TeamPlayer[],
  games: Game[]
): Promise<TeamWithRelations[]> {
  const playerMap = new Map(players.map(p => [p.id, p]))
  
  return teams.map(team => {
    // Get team players for this team
    const teamPlayerRelations = teamPlayers.filter(tp => tp.teamId === team.id)
    
    // Get actual player objects with jersey numbers
    const teamPlayersWithDetails = teamPlayerRelations
      .map(tp => {
        const player = playerMap.get(tp.playerId)
        return player ? { ...player, jerseyNumber: tp.jerseyNumber } : null
      })
      .filter(Boolean) as (Player & { jerseyNumber?: string | number })[]
    
    // Sort by jersey number if available
    teamPlayersWithDetails.sort((a, b) => {
      const aNum = parseInt(String(a.jerseyNumber || '999'), 10)
      const bNum = parseInt(String(b.jerseyNumber || '999'), 10)
      return aNum - bNum
    })
    
    // Get games for this team
    const teamGames = games.filter(g => g.teamId === team.id)
    
    return {
      ...team,
      players: teamPlayersWithDetails,
      games: teamGames
    }
  })
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate denormalized team data before transformation
 */
export function validateDenormalizedData(teams: TeamWithRelations[]): string[] {
  const errors: string[] = []
  
  for (const team of teams) {
    if (!team.id) errors.push(`Team missing ID: ${team.name}`)
    if (!team.name) errors.push(`Team missing name: ${team.id}`)
    if (!team.season) errors.push(`Team missing season: ${team.name}`)
    
    for (const player of team.players) {
      if (!player.firstName) errors.push(`Player missing firstName in team ${team.name}`)
      if (!player.lastName) errors.push(`Player missing lastName in team ${team.name}`)
    }
    
    for (const game of team.games) {
      if (!game.id) errors.push(`Game missing ID in team ${team.name}`)
      if (!game.date) errors.push(`Game missing date in team ${team.name}`)
      if (!game.opponent) errors.push(`Game missing opponent in team ${team.name}`)
      if (!game.homeAway) errors.push(`Game missing homeAway in team ${team.name}`)
      if (!game.inningCount) errors.push(`Game missing inningCount in team ${team.name}`)
    }
  }
  
  return errors
}

/**
 * Generate migration statistics
 */
export function generateMigrationStats(
  players: Player[],
  teams: Team[],
  teamPlayers: TeamPlayer[],
  games: Game[]
): DataMigrationResult {
  return {
    success: true,
    playersCreated: players.length,
    teamsCreated: teams.length,
    teamPlayersCreated: teamPlayers.length,
    gamesCreated: games.length,
    lineupCellsCreated: 0, // Will be updated when lineup cells are migrated
    errors: []
  }
}

// ============================================================================
// MOCK DATA SPECIFIC TRANSFORMATIONS
// ============================================================================

/**
 * Transform the current mock data structure to normalized format
 */
export function transformMockDataToNormalized(mockTeams: TeamWithRelations[]) {
  // Validate input data
  const validationErrors = validateDenormalizedData(mockTeams)
  if (validationErrors.length > 0) {
    throw new Error(`Data validation failed: ${validationErrors.join(', ')}`)
  }
  
  // Extract normalized entities
  const players = extractPlayersFromTeams(mockTeams)
  const teams = extractTeamsFromDenormalized(mockTeams)
  const teamPlayers = createTeamPlayerRelationships(mockTeams, players)
  const games = extractGamesFromTeams(mockTeams)
  
  return {
    players,
    teams,
    teamPlayers,
    games,
    stats: generateMigrationStats(players, teams, teamPlayers, games)
  }
}
