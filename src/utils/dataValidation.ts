import { 
  playerService, 
  teamService, 
  teamPlayerService, 
  gameService,
  lineupService 
} from '@/services'
import type { Player, Team, TeamPlayer, Game, LineupCell } from '@/types'

/**
 * Data validation and integrity checking utilities
 */

// ============================================================================
// ENTITY VALIDATION
// ============================================================================

/**
 * Validate player data
 */
export function validatePlayer(player: Partial<Player>): string[] {
  const errors: string[] = []
  
  if (!player.firstName?.trim()) {
    errors.push('First name is required')
  }
  
  if (!player.lastName?.trim()) {
    errors.push('Last name is required')
  }
  
  if (player.throws && !['L', 'R', 'S'].includes(player.throws)) {
    errors.push('Throws must be L, R, or S')
  }
  
  if (player.bats && !['L', 'R', 'S'].includes(player.bats)) {
    errors.push('Bats must be L, R, or S')
  }
  
  if (player.dob && !isValidDate(player.dob)) {
    errors.push('Date of birth must be a valid date')
  }
  
  return errors
}

/**
 * Validate team data
 */
export function validateTeam(team: Partial<Team>): string[] {
  const errors: string[] = []
  
  if (!team.name?.trim()) {
    errors.push('Team name is required')
  }
  
  if (!team.season?.trim()) {
    errors.push('Season is required')
  }
  
  if (team.name && team.name.length > 100) {
    errors.push('Team name must be 100 characters or less')
  }
  
  return errors
}

/**
 * Validate team-player relationship
 */
export function validateTeamPlayer(teamPlayer: Partial<TeamPlayer>): string[] {
  const errors: string[] = []
  
  if (!teamPlayer.teamId?.trim()) {
    errors.push('Team ID is required')
  }
  
  if (!teamPlayer.playerId?.trim()) {
    errors.push('Player ID is required')
  }
  
  if (teamPlayer.jerseyNumber) {
    const jerseyNum = String(teamPlayer.jerseyNumber)
    if (!/^\d+$/.test(jerseyNum) || parseInt(jerseyNum) < 0 || parseInt(jerseyNum) > 999) {
      errors.push('Jersey number must be a valid number between 0 and 999')
    }
  }
  
  if (teamPlayer.preferredPositions && !Array.isArray(teamPlayer.preferredPositions)) {
    errors.push('Preferred positions must be an array')
  }
  
  return errors
}

/**
 * Validate game data
 */
export function validateGame(game: Partial<Game>): string[] {
  const errors: string[] = []
  
  if (!game.teamId?.trim()) {
    errors.push('Team ID is required')
  }
  
  if (!game.date?.trim()) {
    errors.push('Game date is required')
  } else if (!isValidDate(game.date)) {
    errors.push('Game date must be a valid date')
  }
  
  if (!game.opponent?.trim()) {
    errors.push('Opponent is required')
  }
  
  if (!game.homeAway || !['Home', 'Away'].includes(game.homeAway)) {
    errors.push('Home/Away must be either "Home" or "Away"')
  }
  
  if (!game.inningCount || game.inningCount < 1 || game.inningCount > 20) {
    errors.push('Inning count must be between 1 and 20')
  }
  
  if (game.gameTime && !isValidTime(game.gameTime)) {
    errors.push('Game time must be in valid format (e.g., "6:00 PM")')
  }
  
  if (game.arrivalTime && !isValidTime(game.arrivalTime)) {
    errors.push('Arrival time must be in valid format (e.g., "5:30 PM")')
  }
  
  return errors
}

/**
 * Validate lineup cell data
 */
export function validateLineupCell(lineupCell: Partial<LineupCell>): string[] {
  const errors: string[] = []
  
  if (!lineupCell.gameId?.trim()) {
    errors.push('Game ID is required')
  }
  
  if (!lineupCell.inning || lineupCell.inning < 1 || lineupCell.inning > 20) {
    errors.push('Inning must be between 1 and 20')
  }
  
  if (!lineupCell.positionKey?.trim()) {
    errors.push('Position key is required')
  }
  
  return errors
}

// ============================================================================
// REFERENTIAL INTEGRITY CHECKS
// ============================================================================

/**
 * Check if a player exists
 */
export async function validatePlayerExists(playerId: string): Promise<boolean> {
  return await playerService.exists(playerId)
}

/**
 * Check if a team exists
 */
export async function validateTeamExists(teamId: string): Promise<boolean> {
  return await teamService.exists(teamId)
}

/**
 * Check if a game exists
 */
export async function validateGameExists(gameId: string): Promise<boolean> {
  return await gameService.exists(gameId)
}

/**
 * Validate team-player relationship integrity
 */
export async function validateTeamPlayerIntegrity(teamPlayer: TeamPlayer): Promise<string[]> {
  const errors: string[] = []
  
  // Check if team exists
  if (!(await validateTeamExists(teamPlayer.teamId))) {
    errors.push(`Team with ID ${teamPlayer.teamId} does not exist`)
  }
  
  // Check if player exists
  if (!(await validatePlayerExists(teamPlayer.playerId))) {
    errors.push(`Player with ID ${teamPlayer.playerId} does not exist`)
  }
  
  // Check for duplicate team-player relationship
  const existing = await teamPlayerService.getByTeamId(teamPlayer.teamId)
  if (existing.success && existing.data) {
    const duplicate = existing.data.find(tp => 
      tp.playerId === teamPlayer.playerId && tp.id !== teamPlayer.id
    )
    if (duplicate) {
      errors.push('Player is already on this team')
    }
  }
  
  return errors
}

/**
 * Validate game integrity
 */
export async function validateGameIntegrity(game: Game): Promise<string[]> {
  const errors: string[] = []
  
  // Check if team exists
  if (!(await validateTeamExists(game.teamId))) {
    errors.push(`Team with ID ${game.teamId} does not exist`)
  }
  
  return errors
}

/**
 * Validate lineup cell integrity
 */
export async function validateLineupCellIntegrity(lineupCell: LineupCell): Promise<string[]> {
  const errors: string[] = []
  
  // Check if game exists
  if (!(await validateGameExists(lineupCell.gameId))) {
    errors.push(`Game with ID ${lineupCell.gameId} does not exist`)
  }
  
  // Check if player exists (if assigned)
  if (lineupCell.playerId && !(await validatePlayerExists(lineupCell.playerId))) {
    errors.push(`Player with ID ${lineupCell.playerId} does not exist`)
  }
  
  return errors
}

// ============================================================================
// DATABASE INTEGRITY CHECKS
// ============================================================================

/**
 * Perform comprehensive database integrity check
 */
export async function performIntegrityCheck(): Promise<{
  isValid: boolean
  errors: string[]
  warnings: string[]
}> {
  const errors: string[] = []
  const warnings: string[] = []
  
  try {
    console.log('🔍 Performing database integrity check...')
    
    // Get all data
    const [playersResult, teamsResult, teamPlayersResult, gamesResult, lineupCellsResult] = await Promise.all([
      playerService.getAll(),
      teamService.getAll(),
      teamPlayerService.getAll(),
      gameService.getAll(),
      lineupService.getAll()
    ])
    
    if (!playersResult.success || !teamsResult.success || !teamPlayersResult.success || 
        !gamesResult.success || !lineupCellsResult.success) {
      errors.push('Failed to retrieve data for integrity check')
      return { isValid: false, errors, warnings }
    }
    
    const players = playersResult.data || []
    const teams = teamsResult.data || []
    const teamPlayers = teamPlayersResult.data || []
    const games = gamesResult.data || []
    const lineupCells = lineupCellsResult.data || []
    
    // Check team-player relationships
    for (const teamPlayer of teamPlayers) {
      const teamExists = teams.some(t => t.id === teamPlayer.teamId)
      const playerExists = players.some(p => p.id === teamPlayer.playerId)
      
      if (!teamExists) {
        errors.push(`TeamPlayer references non-existent team: ${teamPlayer.teamId}`)
      }
      if (!playerExists) {
        errors.push(`TeamPlayer references non-existent player: ${teamPlayer.playerId}`)
      }
    }
    
    // Check game relationships
    for (const game of games) {
      const teamExists = teams.some(t => t.id === game.teamId)
      if (!teamExists) {
        errors.push(`Game references non-existent team: ${game.teamId}`)
      }
    }
    
    // Check lineup cell relationships
    for (const lineupCell of lineupCells) {
      const gameExists = games.some(g => g.id === lineupCell.gameId)
      if (!gameExists) {
        errors.push(`LineupCell references non-existent game: ${lineupCell.gameId}`)
      }
      
      if (lineupCell.playerId) {
        const playerExists = players.some(p => p.id === lineupCell.playerId)
        if (!playerExists) {
          errors.push(`LineupCell references non-existent player: ${lineupCell.playerId}`)
        }
      }
    }
    
    // Check for orphaned records
    const usedPlayerIds = new Set([
      ...teamPlayers.map(tp => tp.playerId),
      ...lineupCells.map(lc => lc.playerId).filter(Boolean)
    ])
    
    for (const player of players) {
      if (!usedPlayerIds.has(player.id)) {
        warnings.push(`Player ${player.firstName} ${player.lastName} is not assigned to any team`)
      }
    }
    
    console.log(`✅ Integrity check completed. Errors: ${errors.length}, Warnings: ${warnings.length}`)
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
    
  } catch (error) {
    errors.push(`Integrity check failed: ${error}`)
    return { isValid: false, errors, warnings }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if a string is a valid date
 */
function isValidDate(dateString: string): boolean {
  const date = new Date(dateString)
  return !isNaN(date.getTime()) && dateString.match(/^\d{4}-\d{2}-\d{2}$/) !== null
}

/**
 * Check if a string is a valid time format
 */
function isValidTime(timeString: string): boolean {
  // Simple validation for formats like "6:00 PM", "12:30 AM", etc.
  return /^(1[0-2]|0?[1-9]):[0-5][0-9]\s?(AM|PM)$/i.test(timeString)
}
