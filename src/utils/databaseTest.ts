import { 
  playerService, 
  teamService, 
  teamPlayerService, 
  gameService, 
  lineupService,
  databaseService 
} from '@/services'
import type { Player, Team, Game } from '@/types'

/**
 * Test the database functionality with sample data
 */
export async function testDatabase(): Promise<void> {
  try {
    console.log('🧪 Starting database test...')

    // Test 1: Create a player
    console.log('📝 Testing player creation...')
    const playerResult = await playerService.create({
      id: 'test-player-1',
      firstName: 'Test',
      lastName: 'Player',
      throws: 'R',
      bats: 'R'
    })
    
    if (playerResult.success) {
      console.log('✅ Player created successfully:', playerResult.data?.firstName)
    } else {
      console.error('❌ Failed to create player:', playerResult.error)
      return
    }

    // Test 2: Create a team
    console.log('📝 Testing team creation...')
    const teamResult = await teamService.create({
      id: 'test-team-1',
      name: 'Test Team',
      season: 'Test Season 2025',
      headCoach: 'Test Coach'
    })
    
    if (teamResult.success) {
      console.log('✅ Team created successfully:', teamResult.data?.name)
    } else {
      console.error('❌ Failed to create team:', teamResult.error)
      return
    }

    // Test 3: Add player to team
    console.log('📝 Testing team-player relationship...')
    const teamPlayerResult = await teamPlayerService.addPlayerToTeam(
      'test-team-1',
      'test-player-1',
      {
        jerseyNumber: '99',
        preferredPositions: ['P', '1B']
      }
    )
    
    if (teamPlayerResult.success) {
      console.log('✅ Player added to team successfully')
    } else {
      console.error('❌ Failed to add player to team:', teamPlayerResult.error)
      return
    }

    // Test 4: Create a game
    console.log('📝 Testing game creation...')
    const gameResult = await gameService.create({
      id: 'test-game-1',
      teamId: 'test-team-1',
      date: '2025-09-15',
      opponent: 'Test Opponent',
      homeAway: 'Home',
      inningCount: 7
    })
    
    if (gameResult.success) {
      console.log('✅ Game created successfully:', gameResult.data?.opponent)
    } else {
      console.error('❌ Failed to create game:', gameResult.error)
      return
    }

    // Test 5: Create lineup assignment
    console.log('📝 Testing lineup assignment...')
    const lineupResult = await lineupService.assignPlayer(
      'test-game-1',
      1,
      'P',
      'test-player-1'
    )
    
    if (lineupResult.success) {
      console.log('✅ Lineup assignment created successfully')
    } else {
      console.error('❌ Failed to create lineup assignment:', lineupResult.error)
      return
    }

    // Test 6: Query data
    console.log('📝 Testing data queries...')
    
    const allPlayers = await playerService.getAll()
    const allTeams = await teamService.getAll()
    const teamPlayers = await teamPlayerService.getByTeamId('test-team-1')
    const teamGames = await gameService.getByTeamId('test-team-1')
    const gameLineup = await lineupService.getByGameId('test-game-1')
    
    console.log('📊 Query results:')
    console.log(`   Players: ${allPlayers.data?.length || 0}`)
    console.log(`   Teams: ${allTeams.data?.length || 0}`)
    console.log(`   Team Players: ${teamPlayers.data?.length || 0}`)
    console.log(`   Games: ${teamGames.data?.length || 0}`)
    console.log(`   Lineup Cells: ${gameLineup.data?.length || 0}`)

    // Test 7: Database status
    const status = await databaseService.getStatus()
    console.log('📊 Database status:', status)

    console.log('🎉 All database tests passed!')

  } catch (error) {
    console.error('💥 Database test failed:', error)
    throw error
  }
}

/**
 * Clean up test data
 */
export async function cleanupTestData(): Promise<void> {
  try {
    console.log('🧹 Cleaning up test data...')
    
    await lineupService.clearGameLineup('test-game-1')
    await gameService.delete('test-game-1')
    await teamPlayerService.removePlayerFromTeam('test-team-1', 'test-player-1')
    await teamService.delete('test-team-1')
    await playerService.delete('test-player-1')
    
    console.log('✅ Test data cleaned up successfully')
  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error)
  }
}

// Import additional utilities
import { forceSchemaUpgrade } from './databaseInit'
import { migrateMockDataToDatabase, hasExistingData } from './mockDataMigration'
import { performIntegrityCheck } from './dataValidation'

/**
 * List all available database testing functions
 */
export function listAvailableFunctions(): void {
  console.log('🧪 Available Database Testing Functions:')
  console.log('  - testDatabase() - Run comprehensive database tests')
  console.log('  - cleanupTestData() - Clean up test data')
  console.log('  - forceSchemaUpgrade() - Force database schema upgrade')
  console.log('  - migrateMockData() - Migrate mock data to database')
  console.log('  - hasExistingData() - Check if database has existing data')
  console.log('  - performIntegrityCheck() - Run database integrity check')
  console.log('  - listAvailableFunctions() - Show this list')
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testDatabase = testDatabase
  (window as any).cleanupTestData = cleanupTestData
  (window as any).forceSchemaUpgrade = forceSchemaUpgrade
  (window as any).migrateMockData = migrateMockDataToDatabase
  (window as any).hasExistingData = hasExistingData
  (window as any).performIntegrityCheck = performIntegrityCheck
  (window as any).listAvailableFunctions = listAvailableFunctions

  // Log that functions are available
  console.log('🧪 Database test utilities loaded. Run listAvailableFunctions() to see available commands.')
}
