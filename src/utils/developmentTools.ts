import { databaseService } from '@/services'
import { migrateMockDataToDatabase } from './mockDataMigration'
import { performIntegrityCheck } from './dataValidation'
import { downloadJSONBackup, selectAndImportJSON } from './dataExport'

/**
 * Development tools for debugging and data management
 */

// ============================================================================
// DATABASE MANAGEMENT
// ============================================================================

/**
 * Reset database to empty state
 */
export async function resetDatabase(): Promise<{ success: boolean; error?: string }> {
  try {
    await databaseService.clearAllData()
    console.log('✅ Database reset successfully')
    return { success: true }
  } catch (error) {
    console.error('❌ Failed to reset database:', error)
    return { success: false, error: String(error) }
  }
}

/**
 * Reset database and restore default mock data
 */
export async function resetToDefaultData(): Promise<{ success: boolean; error?: string }> {
  try {
    // Clear existing data
    await databaseService.clearAllData()
    console.log('🗑️ Database cleared')
    
    // Restore mock data
    const migrationResult = await migrateMockDataToDatabase()
    if (!migrationResult.success) {
      throw new Error(migrationResult.errors?.join(', ') || 'Migration failed')
    }
    
    console.log('✅ Default data restored successfully')
    return { success: true }
  } catch (error) {
    console.error('❌ Failed to reset to default data:', error)
    return { success: false, error: String(error) }
  }
}

/**
 * Get database statistics
 */
export async function getDatabaseStats(): Promise<{
  success: boolean
  stats?: {
    teams: number
    players: number
    teamPlayers: number
    games: number
    lineupCells: number
    photos: number
    databaseSize: string
  }
  error?: string
}> {
  try {
    const status = await databaseService.getStatus()
    
    if (!status.isInitialized) {
      throw new Error('Database not initialized')
    }

    // Get counts from each table
    const db = databaseService.getDatabase()
    const [teams, players, teamPlayers, games, lineupCells, photos] = await Promise.all([
      db.teams.count(),
      db.players.count(),
      db.teamPlayers.count(),
      db.games.count(),
      db.lineupCells.count(),
      db.photos.count()
    ])

    // Estimate database size (rough calculation)
    const totalRecords = teams + players + teamPlayers + games + lineupCells + photos
    const estimatedSize = totalRecords * 500 // Rough estimate of 500 bytes per record
    const sizeInKB = Math.round(estimatedSize / 1024)
    const databaseSize = sizeInKB > 1024 ? `${Math.round(sizeInKB / 1024)} MB` : `${sizeInKB} KB`

    return {
      success: true,
      stats: {
        teams,
        players,
        teamPlayers,
        games,
        lineupCells,
        photos,
        databaseSize
      }
    }
  } catch (error) {
    return { success: false, error: String(error) }
  }
}

// ============================================================================
// DATA INSPECTION
// ============================================================================

/**
 * Inspect data for a specific table
 */
export async function inspectTable(tableName: 'teams' | 'players' | 'teamPlayers' | 'games' | 'lineupCells' | 'photos'): Promise<{
  success: boolean
  data?: any[]
  count?: number
  error?: string
}> {
  try {
    const db = databaseService.getDatabase()
    const table = db[tableName]
    
    const data = await table.toArray()
    const count = data.length
    
    console.log(`📊 ${tableName} table:`, { count, data })
    
    return { success: true, data, count }
  } catch (error) {
    return { success: false, error: String(error) }
  }
}

/**
 * Search for records in any table
 */
export async function searchRecords(
  tableName: 'teams' | 'players' | 'teamPlayers' | 'games' | 'lineupCells' | 'photos',
  searchTerm: string,
  field?: string
): Promise<{
  success: boolean
  results?: any[]
  count?: number
  error?: string
}> {
  try {
    const db = databaseService.getDatabase()
    const table = db[tableName]
    
    let results: any[]
    
    if (field) {
      // Search specific field
      results = await table.where(field).startsWithIgnoreCase(searchTerm).toArray()
    } else {
      // Search all string fields
      const allRecords = await table.toArray()
      results = allRecords.filter(record => {
        return Object.values(record).some(value => 
          typeof value === 'string' && 
          value.toLowerCase().includes(searchTerm.toLowerCase())
        )
      })
    }
    
    console.log(`🔍 Search results for "${searchTerm}" in ${tableName}:`, results)
    
    return { success: true, results, count: results.length }
  } catch (error) {
    return { success: false, error: String(error) }
  }
}

// ============================================================================
// DEBUGGING TOOLS
// ============================================================================

/**
 * Log current application state
 */
export async function logApplicationState(): Promise<void> {
  console.group('🔍 Application State Debug')
  
  try {
    // Database status
    const dbStatus = await databaseService.getStatus()
    console.log('📊 Database Status:', dbStatus)
    
    // Database stats
    const statsResult = await getDatabaseStats()
    if (statsResult.success) {
      console.log('📈 Database Statistics:', statsResult.stats)
    }
    
    // Integrity check
    const integrityResult = await performIntegrityCheck()
    console.log('🔒 Integrity Check:', {
      isValid: integrityResult.isValid,
      errors: integrityResult.errors,
      warnings: integrityResult.warnings
    })
    
    // Recent activity (last 10 lineup cells)
    const recentActivity = await inspectTable('lineupCells')
    if (recentActivity.success && recentActivity.data) {
      const recent = recentActivity.data
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 10)
      console.log('⏰ Recent Activity (Last 10 lineup changes):', recent)
    }
    
  } catch (error) {
    console.error('❌ Failed to log application state:', error)
  }
  
  console.groupEnd()
}

/**
 * Performance monitoring
 */
export async function measurePerformance<T>(
  operation: () => Promise<T>,
  operationName: string
): Promise<{ result: T; duration: number }> {
  const startTime = performance.now()
  
  try {
    const result = await operation()
    const endTime = performance.now()
    const duration = Math.round(endTime - startTime)
    
    console.log(`⚡ Performance: ${operationName} took ${duration}ms`)
    
    return { result, duration }
  } catch (error) {
    const endTime = performance.now()
    const duration = Math.round(endTime - startTime)
    
    console.error(`❌ Performance: ${operationName} failed after ${duration}ms:`, error)
    throw error
  }
}

// ============================================================================
// GLOBAL DEVELOPMENT FUNCTIONS
// ============================================================================

/**
 * Make development tools available globally in browser console
 */
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  // Database management
  (window as any).resetDatabase = resetDatabase
  (window as any).resetToDefaultData = resetToDefaultData
  (window as any).getDatabaseStats = getDatabaseStats
  
  // Data inspection
  (window as any).inspectTable = inspectTable
  (window as any).searchRecords = searchRecords
  
  // Debugging
  (window as any).logApplicationState = logApplicationState
  (window as any).measurePerformance = measurePerformance
  
  // Data management
  (window as any).exportBackup = downloadJSONBackup
  (window as any).importBackup = selectAndImportJSON
  (window as any).checkIntegrity = performIntegrityCheck
  
  // Quick access functions
  (window as any).devTools = {
    reset: resetDatabase,
    restore: resetToDefaultData,
    stats: getDatabaseStats,
    inspect: inspectTable,
    search: searchRecords,
    debug: logApplicationState,
    export: downloadJSONBackup,
    import: selectAndImportJSON,
    integrity: performIntegrityCheck
  }
  
  console.log('🛠️ Development tools loaded. Available functions:')
  console.log('  - resetDatabase() - Clear all data')
  console.log('  - resetToDefaultData() - Reset to mock data')
  console.log('  - getDatabaseStats() - Show database statistics')
  console.log('  - inspectTable(tableName) - View table contents')
  console.log('  - searchRecords(table, term) - Search records')
  console.log('  - logApplicationState() - Debug current state')
  console.log('  - exportBackup() - Export data backup')
  console.log('  - importBackup() - Import data backup')
  console.log('  - checkIntegrity() - Validate data integrity')
  console.log('  - devTools.* - Quick access object')
}
