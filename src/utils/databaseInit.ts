import { databaseService } from '@/services'
import { migrateMockDataToDatabase } from './mockDataMigration'

/**
 * Initialize the database and check its status
 * This utility can be called during app startup
 */
export async function initializeDatabase(): Promise<void> {
  try {
    console.log('Initializing database...')
    
    // Initialize the database
    await databaseService.initialize()
    
    // Get and log status
    const status = await databaseService.getStatus()
    console.log('Database status:', status)
    
    if (status.isInitialized) {
      console.log('✅ Database initialized successfully')
      console.log(`📊 Database version: ${status.version}`)
      console.log(`📁 Has data: ${status.hasData}`)

      // Auto-migrate mock data if database is empty
      if (!status.hasData) {
        console.log('📦 Database is empty, migrating mock data...')
        const migrationResult = await migrateMockDataToDatabase()
        if (migrationResult.success) {
          console.log('✅ Mock data migration completed')
        } else {
          console.warn('⚠️ Mock data migration failed:', migrationResult.errors)
        }
      }
    } else {
      console.warn('⚠️ Database initialization may have failed')
    }
  } catch (error) {
    console.error('❌ Failed to initialize database:', error)
    throw error
  }
}

/**
 * Reset database to empty state (useful for development)
 */
export async function resetDatabase(): Promise<void> {
  try {
    console.log('Resetting database...')

    await databaseService.clearAllData()
    console.log('✅ Database reset successfully')
  } catch (error) {
    console.error('❌ Failed to reset database:', error)
    throw error
  }
}

/**
 * Force database schema upgrade by deleting and recreating
 */
export async function forceSchemaUpgrade(): Promise<void> {
  try {
    console.log('🔄 Forcing database schema upgrade...')

    // Close current connection
    await databaseService.close()

    // Delete the database entirely
    await new Promise<void>((resolve, reject) => {
      const deleteReq = indexedDB.deleteDatabase('LineupLabDatabase')
      deleteReq.onsuccess = () => {
        console.log('🗑️ Old database deleted')
        resolve()
      }
      deleteReq.onerror = () => {
        reject(new Error('Failed to delete database'))
      }
    })

    // Reinitialize with new schema
    await databaseService.initialize()
    console.log('✅ Database schema upgraded successfully')
  } catch (error) {
    console.error('❌ Failed to upgrade database schema:', error)
    throw error
  }
}

/**
 * Check if database is ready for use
 */
export async function isDatabaseReady(): Promise<boolean> {
  try {
    return await databaseService.isReady()
  } catch {
    return false
  }
}
