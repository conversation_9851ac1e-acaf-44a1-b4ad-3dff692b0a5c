import { databaseService } from '@/services'

/**
 * Initialize the database and check its status
 * This utility can be called during app startup
 */
export async function initializeDatabase(): Promise<void> {
  try {
    console.log('Initializing database...')
    
    // Initialize the database
    await databaseService.initialize()
    
    // Get and log status
    const status = await databaseService.getStatus()
    console.log('Database status:', status)
    
    if (status.isInitialized) {
      console.log('✅ Database initialized successfully')
      console.log(`📊 Database version: ${status.version}`)
      console.log(`📁 Has data: ${status.hasData}`)
    } else {
      console.warn('⚠️ Database initialization may have failed')
    }
  } catch (error) {
    console.error('❌ Failed to initialize database:', error)
    throw error
  }
}

/**
 * Reset database to empty state (useful for development)
 */
export async function resetDatabase(): Promise<void> {
  try {
    console.log('Resetting database...')
    
    await databaseService.clearAllData()
    console.log('✅ Database reset successfully')
  } catch (error) {
    console.error('❌ Failed to reset database:', error)
    throw error
  }
}

/**
 * Check if database is ready for use
 */
export async function isDatabaseReady(): Promise<boolean> {
  try {
    return await databaseService.isReady()
  } catch {
    return false
  }
}
