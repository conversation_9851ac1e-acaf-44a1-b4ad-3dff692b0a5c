import { teamService, gameService, lineupService, playerService, teamPlayerService } from '@/services'
import type { TeamWithRelations, Game, LineupCell, Player, Team, TeamPlayer } from '@/types'

/**
 * Data export and import utilities for backup and sharing
 */

// ============================================================================
// JSON EXPORT (FULL BACKUP)
// ============================================================================

/**
 * Export all application data as JSON for backup purposes
 */
export async function exportAllDataAsJSON(): Promise<{
  success: boolean
  data?: string
  filename?: string
  error?: string
}> {
  try {
    // Get all data from services
    const [teamsResult, playersResult, teamPlayersResult, gamesResult, lineupCellsResult] = await Promise.all([
      teamService.getAll(),
      playerService.getAll(),
      teamPlayerService.getAll(),
      gameService.getAll(),
      lineupService.getAll()
    ])

    // Check if all requests succeeded
    if (!teamsResult.success || !playersResult.success || !teamPlayersResult.success || 
        !gamesResult.success || !lineupCellsResult.success) {
      throw new Error('Failed to retrieve all data for export')
    }

    // Create export data structure
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0',
        application: 'Lineup Lab',
        description: 'Complete application data backup'
      },
      data: {
        teams: teamsResult.data || [],
        players: playersResult.data || [],
        teamPlayers: teamPlayersResult.data || [],
        games: gamesResult.data || [],
        lineupCells: lineupCellsResult.data || []
      },
      statistics: {
        totalTeams: (teamsResult.data || []).length,
        totalPlayers: (playersResult.data || []).length,
        totalGames: (gamesResult.data || []).length,
        totalLineupCells: (lineupCellsResult.data || []).length
      }
    }

    // Convert to JSON string
    const jsonString = JSON.stringify(exportData, null, 2)
    
    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `lineup-lab-backup-${timestamp}.json`

    return {
      success: true,
      data: jsonString,
      filename
    }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

/**
 * Download JSON backup file
 */
export async function downloadJSONBackup(): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await exportAllDataAsJSON()
    
    if (!result.success || !result.data || !result.filename) {
      throw new Error(result.error || 'Failed to generate backup data')
    }

    // Create and trigger download
    const blob = new Blob([result.data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = result.filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

// ============================================================================
// CSV EXPORT (LINEUPS)
// ============================================================================

/**
 * Export game lineup as CSV for Excel/Google Sheets
 */
export async function exportGameLineupAsCSV(gameId: string): Promise<{
  success: boolean
  data?: string
  filename?: string
  error?: string
}> {
  try {
    // Get game and lineup data
    const [gameResult, lineupResult] = await Promise.all([
      gameService.getById(gameId),
      lineupService.getByGameId(gameId)
    ])

    if (!gameResult.success || !lineupResult.success) {
      throw new Error('Failed to retrieve game or lineup data')
    }

    const game = gameResult.data
    const lineupCells = lineupResult.data || []

    if (!game) {
      throw new Error('Game not found')
    }

    // Get team data to get player names
    const teamResult = await teamService.getTeamWithRelations(game.teamId)
    if (!teamResult.success || !teamResult.data) {
      throw new Error('Failed to retrieve team data')
    }

    const team = teamResult.data

    // Create player lookup map
    const playerMap = new Map<string, { firstName: string; lastName: string; jerseyNumber?: string | number }>()
    team.players.forEach(player => {
      playerMap.set(player.id, {
        firstName: player.firstName,
        lastName: player.lastName,
        jerseyNumber: player.jerseyNumber
      })
    })

    // Define positions in order
    const positions = ['P', 'C', '1B', '2B', 'SS', '3B', 'LF', 'CF', 'RF', 'Bench1', 'Bench2']
    
    // Create CSV header
    const headers = ['Position', ...Array.from({ length: game.inningCount }, (_, i) => `Inning ${i + 1}`)]
    
    // Create CSV rows
    const rows: string[][] = [headers]
    
    positions.forEach(position => {
      const row = [position]
      
      for (let inning = 1; inning <= game.inningCount; inning++) {
        const cell = lineupCells.find(c => c.inning === inning && c.positionKey === position)
        
        if (cell && cell.playerId) {
          const player = playerMap.get(cell.playerId)
          if (player) {
            const playerName = `${player.firstName} ${player.lastName}`
            const jerseyInfo = player.jerseyNumber ? ` (#${player.jerseyNumber})` : ''
            row.push(`${playerName}${jerseyInfo}`)
          } else {
            row.push('Unknown Player')
          }
        } else {
          row.push('')
        }
      }
      
      rows.push(row)
    })

    // Convert to CSV string
    const csvContent = rows.map(row => 
      row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n')

    // Add metadata header
    const metadata = [
      `"Game: ${game.opponent} (${game.homeAway})"`,
      `"Date: ${game.date}"`,
      `"Team: ${team.name}"`,
      `"Exported: ${new Date().toLocaleString()}"`,
      '""', // Empty row
    ].join('\n')

    const fullCSV = metadata + '\n' + csvContent

    // Generate filename
    const dateStr = game.date.replace(/-/g, '')
    const opponentStr = game.opponent.replace(/[^a-zA-Z0-9]/g, '')
    const filename = `lineup-${team.name.replace(/[^a-zA-Z0-9]/g, '')}-vs-${opponentStr}-${dateStr}.csv`

    return {
      success: true,
      data: fullCSV,
      filename
    }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

/**
 * Download CSV lineup file
 */
export async function downloadLineupCSV(gameId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await exportGameLineupAsCSV(gameId)
    
    if (!result.success || !result.data || !result.filename) {
      throw new Error(result.error || 'Failed to generate CSV data')
    }

    // Create and trigger download
    const blob = new Blob([result.data], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = result.filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

// ============================================================================
// TEAM ROSTER EXPORT
// ============================================================================

/**
 * Export team roster as CSV
 */
export async function exportTeamRosterAsCSV(teamId: string): Promise<{
  success: boolean
  data?: string
  filename?: string
  error?: string
}> {
  try {
    const teamResult = await teamService.getTeamWithRelations(teamId)
    
    if (!teamResult.success || !teamResult.data) {
      throw new Error('Failed to retrieve team data')
    }

    const team = teamResult.data

    // Create CSV header
    const headers = ['Jersey #', 'First Name', 'Last Name', 'Throws', 'Bats', 'Preferred Positions']
    
    // Create CSV rows
    const rows: string[][] = [headers]
    
    team.players
      .sort((a, b) => {
        const aNum = parseInt(String(a.jerseyNumber || '999'), 10)
        const bNum = parseInt(String(b.jerseyNumber || '999'), 10)
        return aNum - bNum
      })
      .forEach(player => {
        // Get team-player relationship for preferred positions
        const teamPlayer = team.teamPlayers?.find(tp => tp.playerId === player.id)
        const preferredPositions = teamPlayer?.preferredPositions?.join(', ') || ''
        
        rows.push([
          String(player.jerseyNumber || ''),
          player.firstName,
          player.lastName,
          player.throws || '',
          player.bats || '',
          preferredPositions
        ])
      })

    // Convert to CSV string
    const csvContent = rows.map(row => 
      row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n')

    // Add metadata header
    const metadata = [
      `"Team: ${team.name}"`,
      `"Season: ${team.season}"`,
      `"Head Coach: ${team.headCoach || 'Not specified'}"`,
      `"Total Players: ${team.players.length}"`,
      `"Exported: ${new Date().toLocaleString()}"`,
      '""', // Empty row
    ].join('\n')

    const fullCSV = metadata + '\n' + csvContent

    // Generate filename
    const teamNameStr = team.name.replace(/[^a-zA-Z0-9]/g, '')
    const seasonStr = team.season.replace(/[^a-zA-Z0-9]/g, '')
    const filename = `roster-${teamNameStr}-${seasonStr}.csv`

    return {
      success: true,
      data: fullCSV,
      filename
    }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

/**
 * Download team roster CSV
 */
export async function downloadTeamRosterCSV(teamId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await exportTeamRosterAsCSV(teamId)
    
    if (!result.success || !result.data || !result.filename) {
      throw new Error(result.error || 'Failed to generate roster CSV')
    }

    // Create and trigger download
    const blob = new Blob([result.data], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = result.filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

// ============================================================================
// JSON IMPORT (RESTORE FROM BACKUP)
// ============================================================================

/**
 * Import data from JSON backup file
 */
export async function importDataFromJSON(jsonData: string): Promise<{
  success: boolean
  statistics?: {
    teamsImported: number
    playersImported: number
    gamesImported: number
    lineupCellsImported: number
  }
  error?: string
}> {
  try {
    // Parse JSON data
    const importData = JSON.parse(jsonData)

    // Validate structure
    if (!importData.data || typeof importData.data !== 'object') {
      throw new Error('Invalid backup file format: missing data section')
    }

    const { teams, players, teamPlayers, games, lineupCells } = importData.data

    // Validate required arrays
    if (!Array.isArray(teams) || !Array.isArray(players) || !Array.isArray(teamPlayers) ||
        !Array.isArray(games) || !Array.isArray(lineupCells)) {
      throw new Error('Invalid backup file format: data arrays are missing or invalid')
    }

    // Import data in correct order (respecting dependencies)
    let teamsImported = 0
    let playersImported = 0
    let gamesImported = 0
    let lineupCellsImported = 0

    // 1. Import players first (no dependencies)
    for (const player of players) {
      const result = await playerService.create(player)
      if (result.success) {
        playersImported++
      }
    }

    // 2. Import teams (no dependencies)
    for (const team of teams) {
      const result = await teamService.create(team)
      if (result.success) {
        teamsImported++
      }
    }

    // 3. Import team-player relationships (depends on teams and players)
    for (const teamPlayer of teamPlayers) {
      await teamPlayerService.create(teamPlayer)
    }

    // 4. Import games (depends on teams)
    for (const game of games) {
      const result = await gameService.create(game)
      if (result.success) {
        gamesImported++
      }
    }

    // 5. Import lineup cells (depends on games and players)
    for (const lineupCell of lineupCells) {
      const result = await lineupService.create(lineupCell)
      if (result.success) {
        lineupCellsImported++
      }
    }

    return {
      success: true,
      statistics: {
        teamsImported,
        playersImported,
        gamesImported,
        lineupCellsImported
      }
    }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

/**
 * Handle file upload and import
 */
export async function handleJSONImport(file: File): Promise<{
  success: boolean
  statistics?: {
    teamsImported: number
    playersImported: number
    gamesImported: number
    lineupCellsImported: number
  }
  error?: string
}> {
  try {
    // Validate file type
    if (!file.name.endsWith('.json')) {
      throw new Error('Please select a JSON file')
    }

    // Read file content
    const fileContent = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })

    // Import data
    return await importDataFromJSON(fileContent)
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create file input element for import
 */
export function createFileInput(
  accept: string = '.json',
  onFileSelected: (file: File) => void
): HTMLInputElement {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = accept
  input.style.display = 'none'

  input.addEventListener('change', (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      onFileSelected(file)
    }
  })

  return input
}

/**
 * Trigger file selection dialog for JSON import
 */
export async function selectAndImportJSON(): Promise<{
  success: boolean
  statistics?: {
    teamsImported: number
    playersImported: number
    gamesImported: number
    lineupCellsImported: number
  }
  error?: string
}> {
  return new Promise((resolve) => {
    const input = createFileInput('.json', async (file) => {
      const result = await handleJSONImport(file)
      document.body.removeChild(input)
      resolve(result)
    })

    document.body.appendChild(input)
    input.click()
  })
}
