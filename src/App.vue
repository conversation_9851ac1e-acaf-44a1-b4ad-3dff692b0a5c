<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useTheme } from 'vuetify'
import { useRoute, useRouter } from 'vue-router'

// Theme setup
const theme = useTheme()
const route = useRoute()
const router = useRouter()

// Navigation methods
const switchToClickMode = () => {
  router.push({
    path: '/click-mode',
    query: route.query
  })
}

const switchToDragMode = () => {
  router.push({
    path: '/drag-mode',
    query: route.query
  })
}

// Current mode display
const currentMode = computed(() => {
  return route.name === 'drag-mode' ? 'Drag & Drop' : 'Click'
})

// Lifecycle hooks
onMounted(() => {
  // Set initial theme
  theme.global.name.value = 'light'
})
</script>

<template>
  <v-app>
    <v-app-bar app color="primary" dark>
      <v-toolbar-title>⚾ Baseball Lineup Manager</v-toolbar-title>

      <v-spacer></v-spacer>

      <!-- Mode Switch Buttons -->
      <v-btn-toggle
        :model-value="route.name"
        color="white"
        variant="outlined"
        density="comfortable"
        class="mr-4"
      >
        <v-btn
          value="click-mode"
          @click="switchToClickMode"
          size="small"
        >
          <v-icon start>mdi-cursor-default-click</v-icon>
          Click Mode
        </v-btn>
        <v-btn
          value="drag-mode"
          @click="switchToDragMode"
          size="small"
        >
          <v-icon start>mdi-drag</v-icon>
          Drag Mode
        </v-btn>
      </v-btn-toggle>

      <v-btn icon>
        <v-icon>mdi-theme-light-dark</v-icon>
      </v-btn>
    </v-app-bar>

    <v-main class="overflow-hidden">
      <RouterView />
    </v-main>

    <v-footer app color="grey-lighten-4">
      <v-chip
        :color="route.name === 'drag-mode' ? 'orange' : 'blue'"
        size="small"
        class="mr-2"
      >
        {{ currentMode }} Mode
      </v-chip>
      <v-spacer></v-spacer>
      <div>Baseball Lineup Manager &copy; {{ new Date().getFullYear() }}</div>
    </v-footer>
  </v-app>
</template>

<style>
html, body {
  overflow: hidden !important;
}
</style>
