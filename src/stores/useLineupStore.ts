import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { lineupService } from '@/services'
import type { LineupCell, ServiceResponse, GridCell } from '@/types'

export const useLineupStore = defineStore('lineup', () => {
  // ============================================================================
  // STATE
  // ============================================================================
  
  const lineupCells = ref<LineupCell[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const autoSaveEnabled = ref(true)
  const lastSaved = ref<string | null>(null)

  // ============================================================================
  // GETTERS
  // ============================================================================

  /**
   * Get lineup cells for a specific game
   */
  const getGameLineup = computed(() => {
    return (gameId: string): LineupCell[] => {
      return lineupCells.value
        .filter(cell => cell.gameId === gameId)
        .sort((a, b) => {
          // Sort by inning first, then by position
          if (a.inning !== b.inning) {
            return a.inning - b.inning
          }
          return a.positionKey.localeCompare(b.positionKey)
        })
    }
  })

  /**
   * Get lineup cells for a specific inning
   */
  const getInningLineup = computed(() => {
    return (gameId: string, inning: number): LineupCell[] => {
      return lineupCells.value
        .filter(cell => cell.gameId === gameId && cell.inning === inning)
        .sort((a, b) => a.positionKey.localeCompare(b.positionKey))
    }
  })

  /**
   * Get lineup cells for a specific position across all innings
   */
  const getPositionLineup = computed(() => {
    return (gameId: string, positionKey: string): LineupCell[] => {
      return lineupCells.value
        .filter(cell => cell.gameId === gameId && cell.positionKey === positionKey)
        .sort((a, b) => a.inning - b.inning)
    }
  })

  /**
   * Get all assignments for a specific player in a game
   */
  const getPlayerAssignments = computed(() => {
    return (gameId: string, playerId: string): LineupCell[] => {
      return lineupCells.value
        .filter(cell => cell.gameId === gameId && cell.playerId === playerId)
        .sort((a, b) => a.inning - b.inning)
    }
  })

  /**
   * Get lineup cell by specific position and inning
   */
  const getLineupCell = computed(() => {
    return (gameId: string, inning: number, positionKey: string): LineupCell | undefined => {
      return lineupCells.value.find(cell => 
        cell.gameId === gameId && 
        cell.inning === inning && 
        cell.positionKey === positionKey
      )
    }
  })

  /**
   * Convert lineup cells to grid format for UI compatibility
   */
  const getGameLineupAsGrid = computed(() => {
    return (gameId: string): GridCell[] => {
      return lineupCells.value
        .filter(cell => cell.gameId === gameId)
        .map(cell => ({
          gameId: cell.gameId,
          inning: cell.inning,
          positionKey: cell.positionKey,
          playerId: cell.playerId
        }))
    }
  })

  /**
   * Get lineup completion statistics
   */
  const getLineupStats = computed(() => {
    return (gameId: string) => {
      const gameCells = lineupCells.value.filter(cell => cell.gameId === gameId)
      const totalCells = gameCells.length
      const filledCells = gameCells.filter(cell => cell.playerId).length
      const uniquePlayers = new Set(gameCells.map(cell => cell.playerId).filter(Boolean)).size

      return {
        totalCells,
        filledCells,
        emptyCells: totalCells - filledCells,
        completionPercentage: totalCells > 0 ? Math.round((filledCells / totalCells) * 100) : 0,
        uniquePlayersUsed: uniquePlayers
      }
    }
  })

  // ============================================================================
  // ACTIONS
  // ============================================================================

  /**
   * Load lineup for a specific game
   */
  async function loadGameLineup(gameId: string): Promise<ServiceResponse<LineupCell[]>> {
    loading.value = true
    error.value = null

    try {
      const result = await lineupService.getByGameId(gameId)
      
      if (result.success && result.data) {
        // Update lineup cells array with game lineup (merge with existing)
        const gameLineup = result.data
        const otherCells = lineupCells.value.filter(cell => cell.gameId !== gameId)
        lineupCells.value = [...otherCells, ...gameLineup]
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Assign a player to a specific position and inning
   */
  async function assignPlayer(
    gameId: string, 
    inning: number, 
    positionKey: string, 
    playerId: string | null
  ): Promise<ServiceResponse<LineupCell>> {
    if (!autoSaveEnabled.value) {
      // If auto-save is disabled, just update local state
      const cellId = `${gameId}:i${inning}:pos${positionKey}`
      const existingIndex = lineupCells.value.findIndex(cell => cell.id === cellId)
      
      const cell: LineupCell = {
        id: cellId,
        gameId,
        inning,
        positionKey,
        playerId,
        createdAt: new Date().toISOString()
      }

      if (existingIndex !== -1) {
        lineupCells.value[existingIndex] = cell
      } else {
        lineupCells.value.push(cell)
      }

      return {
        success: true,
        data: cell,
        timestamp: new Date().toISOString()
      }
    }

    loading.value = true
    error.value = null

    try {
      const result = await lineupService.assignPlayer(gameId, inning, positionKey, playerId)
      
      if (result.success && result.data) {
        // Update local state
        const cellId = result.data.id
        const existingIndex = lineupCells.value.findIndex(cell => cell.id === cellId)
        
        if (existingIndex !== -1) {
          lineupCells.value[existingIndex] = result.data
        } else {
          lineupCells.value.push(result.data)
        }

        lastSaved.value = new Date().toISOString()
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Clear a player assignment
   */
  async function clearAssignment(
    gameId: string, 
    inning: number, 
    positionKey: string
  ): Promise<ServiceResponse<LineupCell>> {
    return await assignPlayer(gameId, inning, positionKey, null)
  }

  /**
   * Move a player from one position to another
   */
  async function movePlayer(
    gameId: string,
    fromInning: number,
    fromPosition: string,
    toInning: number,
    toPosition: string
  ): Promise<ServiceResponse<{ from: LineupCell; to: LineupCell }>> {
    loading.value = true
    error.value = null

    try {
      const result = await lineupService.movePlayer(
        gameId, fromInning, fromPosition, toInning, toPosition
      )
      
      if (result.success && result.data) {
        // Update local state
        const fromCellId = result.data.from.id
        const toCellId = result.data.to.id
        
        const fromIndex = lineupCells.value.findIndex(cell => cell.id === fromCellId)
        const toIndex = lineupCells.value.findIndex(cell => cell.id === toCellId)
        
        if (fromIndex !== -1) {
          lineupCells.value[fromIndex] = result.data.from
        }
        
        if (toIndex !== -1) {
          lineupCells.value[toIndex] = result.data.to
        } else {
          lineupCells.value.push(result.data.to)
        }

        lastSaved.value = new Date().toISOString()
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Copy a player to another position
   */
  async function copyPlayer(
    gameId: string,
    fromInning: number,
    fromPosition: string,
    toInning: number,
    toPosition: string
  ): Promise<ServiceResponse<LineupCell>> {
    loading.value = true
    error.value = null

    try {
      const result = await lineupService.copyPlayer(
        gameId, fromInning, fromPosition, toInning, toPosition
      )
      
      if (result.success && result.data) {
        // Update local state
        const cellId = result.data.id
        const existingIndex = lineupCells.value.findIndex(cell => cell.id === cellId)
        
        if (existingIndex !== -1) {
          lineupCells.value[existingIndex] = result.data
        } else {
          lineupCells.value.push(result.data)
        }

        lastSaved.value = new Date().toISOString()
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Clear entire game lineup
   */
  async function clearGameLineup(gameId: string): Promise<ServiceResponse<void>> {
    loading.value = true
    error.value = null

    try {
      const result = await lineupService.clearGameLineup(gameId)
      
      if (result.success) {
        // Remove from local state
        lineupCells.value = lineupCells.value.filter(cell => cell.gameId !== gameId)
        lastSaved.value = new Date().toISOString()
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Initialize empty lineup grid for a game
   */
  async function initializeGameLineup(
    gameId: string, 
    inningCount: number, 
    positions: string[]
  ): Promise<ServiceResponse<LineupCell[]>> {
    loading.value = true
    error.value = null

    try {
      const result = await lineupService.initializeGameLineup(gameId, inningCount, positions)
      
      if (result.success && result.data) {
        // Add to local state
        const otherCells = lineupCells.value.filter(cell => cell.gameId !== gameId)
        lineupCells.value = [...otherCells, ...result.data]
        lastSaved.value = new Date().toISOString()
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Toggle auto-save functionality
   */
  function setAutoSave(enabled: boolean): void {
    autoSaveEnabled.value = enabled
  }

  /**
   * Clear all data (useful for testing)
   */
  function clearData(): void {
    lineupCells.value = []
    error.value = null
    lastSaved.value = null
  }

  // ============================================================================
  // RETURN STORE
  // ============================================================================

  return {
    // State
    lineupCells,
    loading,
    error,
    autoSaveEnabled,
    lastSaved,

    // Getters
    getGameLineup,
    getInningLineup,
    getPositionLineup,
    getPlayerAssignments,
    getLineupCell,
    getGameLineupAsGrid,
    getLineupStats,

    // Actions
    loadGameLineup,
    assignPlayer,
    clearAssignment,
    movePlayer,
    copyPlayer,
    clearGameLineup,
    initializeGameLineup,
    setAutoSave,
    clearData
  }
})
