import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { gameService } from '@/services'
import type { Game, ServiceResponse } from '@/types'

export const useGameStore = defineStore('game', () => {
  // ============================================================================
  // STATE
  // ============================================================================
  
  const games = ref<Game[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // ============================================================================
  // GETTERS
  // ============================================================================

  /**
   * Get game by ID
   */
  const getGameById = computed(() => {
    return (id: string): Game | undefined => {
      return games.value.find(game => game.id === id)
    }
  })

  /**
   * Get games for a specific team
   */
  const getGamesByTeamId = computed(() => {
    return (teamId: string): Game[] => {
      return games.value
        .filter(game => game.teamId === teamId)
        .sort((a, b) => a.date.localeCompare(b.date))
    }
  })

  /**
   * Get upcoming games (from today forward)
   */
  const getUpcomingGames = computed(() => {
    return (teamId?: string): Game[] => {
      const today = new Date().toISOString().split('T')[0]
      return games.value
        .filter(game => {
          const matchesTeam = !teamId || game.teamId === teamId
          const isFuture = game.date >= today
          return matchesTeam && isFuture
        })
        .sort((a, b) => a.date.localeCompare(b.date))
    }
  })

  /**
   * Get past games (before today)
   */
  const getPastGames = computed(() => {
    return (teamId?: string): Game[] => {
      const today = new Date().toISOString().split('T')[0]
      return games.value
        .filter(game => {
          const matchesTeam = !teamId || game.teamId === teamId
          const isPast = game.date < today
          return matchesTeam && isPast
        })
        .sort((a, b) => b.date.localeCompare(a.date)) // Most recent first
    }
  })

  /**
   * Get games by home/away status
   */
  const getGamesByHomeAway = computed(() => {
    return (homeAway: 'Home' | 'Away', teamId?: string): Game[] => {
      return games.value
        .filter(game => {
          const matchesTeam = !teamId || game.teamId === teamId
          const matchesHomeAway = game.homeAway === homeAway
          return matchesTeam && matchesHomeAway
        })
        .sort((a, b) => a.date.localeCompare(b.date))
    }
  })

  /**
   * Get games by opponent
   */
  const getGamesByOpponent = computed(() => {
    return (opponent: string, teamId?: string): Game[] => {
      const searchTerm = opponent.toLowerCase()
      return games.value
        .filter(game => {
          const matchesTeam = !teamId || game.teamId === teamId
          const matchesOpponent = game.opponent.toLowerCase().includes(searchTerm)
          return matchesTeam && matchesOpponent
        })
        .sort((a, b) => a.date.localeCompare(b.date))
    }
  })

  /**
   * Get games in a date range
   */
  const getGamesByDateRange = computed(() => {
    return (startDate: string, endDate: string, teamId?: string): Game[] => {
      return games.value
        .filter(game => {
          const matchesTeam = !teamId || game.teamId === teamId
          const inDateRange = game.date >= startDate && game.date <= endDate
          return matchesTeam && inDateRange
        })
        .sort((a, b) => a.date.localeCompare(b.date))
    }
  })

  // ============================================================================
  // ACTIONS
  // ============================================================================

  /**
   * Load all games
   */
  async function loadGames(): Promise<ServiceResponse<Game[]>> {
    loading.value = true
    error.value = null

    try {
      const result = await gameService.getAll()
      
      if (result.success) {
        games.value = result.data || []
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Load games for a specific team
   */
  async function loadTeamGames(teamId: string): Promise<ServiceResponse<Game[]>> {
    loading.value = true
    error.value = null

    try {
      const result = await gameService.getByTeamId(teamId)
      
      if (result.success && result.data) {
        // Update games array with team games (merge with existing)
        const teamGames = result.data
        const otherGames = games.value.filter(game => game.teamId !== teamId)
        games.value = [...otherGames, ...teamGames]
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Create a new game
   */
  async function createGame(gameData: Omit<Game, 'id'>): Promise<ServiceResponse<Game>> {
    loading.value = true
    error.value = null

    try {
      const result = await gameService.create(gameData)
      
      if (result.success && result.data) {
        games.value.push(result.data)
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Update an existing game
   */
  async function updateGame(id: string, updates: Partial<Game>): Promise<ServiceResponse<Game>> {
    loading.value = true
    error.value = null

    try {
      const result = await gameService.update(id, updates)
      
      if (result.success && result.data) {
        const index = games.value.findIndex(game => game.id === id)
        if (index !== -1) {
          games.value[index] = result.data
        }
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Delete a game
   */
  async function deleteGame(id: string): Promise<ServiceResponse<void>> {
    loading.value = true
    error.value = null

    try {
      const result = await gameService.delete(id)
      
      if (result.success) {
        games.value = games.value.filter(game => game.id !== id)
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Check for scheduling conflicts
   */
  async function checkSchedulingConflict(
    teamId: string, 
    date: string, 
    gameTime?: string, 
    excludeGameId?: string
  ): Promise<ServiceResponse<Game[]>> {
    try {
      return await gameService.checkSchedulingConflict(teamId, date, gameTime, excludeGameId)
    } catch (err) {
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get game statistics
   */
  async function getGameStats(gameId: string): Promise<ServiceResponse<any>> {
    try {
      return await gameService.getGameStats(gameId)
    } catch (err) {
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Clear all data (useful for testing)
   */
  function clearData(): void {
    games.value = []
    error.value = null
  }

  // ============================================================================
  // RETURN STORE
  // ============================================================================

  return {
    // State
    games,
    loading,
    error,

    // Getters
    getGameById,
    getGamesByTeamId,
    getUpcomingGames,
    getPastGames,
    getGamesByHomeAway,
    getGamesByOpponent,
    getGamesByDateRange,

    // Actions
    loadGames,
    loadTeamGames,
    createGame,
    updateGame,
    deleteGame,
    checkSchedulingConflict,
    getGameStats,
    clearData
  }
})
