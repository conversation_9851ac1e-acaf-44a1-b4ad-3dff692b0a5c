import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { teamService, teamPlayerService, playerService } from '@/services'
import type { Team, Player, TeamPlayer, TeamWithRelations, ServiceResponse } from '@/types'

export const useTeamStore = defineStore('team', () => {
  // ============================================================================
  // STATE
  // ============================================================================
  
  const teams = ref<Team[]>([])
  const teamPlayers = ref<TeamPlayer[]>([])
  const players = ref<Player[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // ============================================================================
  // GETTERS
  // ============================================================================

  /**
   * Get teams with their players and games (denormalized for UI compatibility)
   */
  const teamsWithRelations = computed((): TeamWithRelations[] => {
    return teams.value.map(team => {
      // Get team players for this team
      const teamPlayerRelations = teamPlayers.value.filter(tp => tp.teamId === team.id)
      
      // Get actual player objects with jersey numbers
      const teamPlayersWithDetails = teamPlayerRelations
        .map(tp => {
          const player = players.value.find(p => p.id === tp.playerId)
          return player ? { ...player, jerseyNumber: tp.jerseyNumber } : null
        })
        .filter(Boolean) as (Player & { jerseyNumber?: string | number })[]
      
      // Sort by jersey number if available
      teamPlayersWithDetails.sort((a, b) => {
        const aNum = parseInt(String(a.jerseyNumber || '999'), 10)
        const bNum = parseInt(String(b.jerseyNumber || '999'), 10)
        return aNum - bNum
      })
      
      return {
        ...team,
        players: teamPlayersWithDetails,
        games: [] // Will be populated by game store
      }
    })
  })

  /**
   * Get team by ID
   */
  const getTeamById = computed(() => {
    return (id: string): Team | undefined => {
      return teams.value.find(team => team.id === id)
    }
  })

  /**
   * Get team with relations by ID
   */
  const getTeamWithRelationsById = computed(() => {
    return (id: string): TeamWithRelations | undefined => {
      return teamsWithRelations.value.find(team => team.id === id)
    }
  })

  /**
   * Get players for a specific team
   */
  const getTeamPlayers = computed(() => {
    return (teamId: string): (Player & { jerseyNumber?: string | number })[] => {
      const teamPlayerRelations = teamPlayers.value.filter(tp => tp.teamId === teamId)
      
      return teamPlayerRelations
        .map(tp => {
          const player = players.value.find(p => p.id === tp.playerId)
          return player ? { ...player, jerseyNumber: tp.jerseyNumber } : null
        })
        .filter(Boolean) as (Player & { jerseyNumber?: string | number })[]
    }
  })

  /**
   * Get teams by season
   */
  const getTeamsBySeason = computed(() => {
    return (season: string): TeamWithRelations[] => {
      return teamsWithRelations.value.filter(team => team.season === season)
    }
  })

  /**
   * Get all unique seasons
   */
  const allSeasons = computed((): string[] => {
    return [...new Set(teams.value.map(team => team.season))].sort()
  })

  // ============================================================================
  // ACTIONS
  // ============================================================================

  /**
   * Load all teams and related data
   */
  async function loadTeams(): Promise<ServiceResponse<Team[]>> {
    loading.value = true
    error.value = null

    try {
      // Load teams, players, and team-player relationships in parallel
      const [teamsResult, playersResult, teamPlayersResult] = await Promise.all([
        teamService.getAll(),
        playerService.getAll(),
        teamPlayerService.getAll()
      ])

      if (!teamsResult.success) {
        throw new Error(teamsResult.error || 'Failed to load teams')
      }
      if (!playersResult.success) {
        throw new Error(playersResult.error || 'Failed to load players')
      }
      if (!teamPlayersResult.success) {
        throw new Error(teamPlayersResult.error || 'Failed to load team players')
      }

      teams.value = teamsResult.data || []
      players.value = playersResult.data || []
      teamPlayers.value = teamPlayersResult.data || []

      return teamsResult
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Create a new team
   */
  async function createTeam(teamData: Omit<Team, 'id'>): Promise<ServiceResponse<Team>> {
    loading.value = true
    error.value = null

    try {
      const result = await teamService.create(teamData)
      
      if (result.success && result.data) {
        teams.value.push(result.data)
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Update an existing team
   */
  async function updateTeam(id: string, updates: Partial<Team>): Promise<ServiceResponse<Team>> {
    loading.value = true
    error.value = null

    try {
      const result = await teamService.update(id, updates)
      
      if (result.success && result.data) {
        const index = teams.value.findIndex(team => team.id === id)
        if (index !== -1) {
          teams.value[index] = result.data
        }
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Delete a team
   */
  async function deleteTeam(id: string): Promise<ServiceResponse<void>> {
    loading.value = true
    error.value = null

    try {
      const result = await teamService.delete(id)
      
      if (result.success) {
        teams.value = teams.value.filter(team => team.id !== id)
        // Also remove related team-player relationships
        teamPlayers.value = teamPlayers.value.filter(tp => tp.teamId !== id)
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Add a player to a team
   */
  async function addPlayerToTeam(
    teamId: string, 
    playerId: string, 
    options?: { jerseyNumber?: string | number; preferredPositions?: string[] }
  ): Promise<ServiceResponse<TeamPlayer>> {
    loading.value = true
    error.value = null

    try {
      const result = await teamPlayerService.addPlayerToTeam(teamId, playerId, options)
      
      if (result.success && result.data) {
        teamPlayers.value.push(result.data)
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Remove a player from a team
   */
  async function removePlayerFromTeam(teamId: string, playerId: string): Promise<ServiceResponse<void>> {
    loading.value = true
    error.value = null

    try {
      const result = await teamPlayerService.removePlayerFromTeam(teamId, playerId)
      
      if (result.success) {
        teamPlayers.value = teamPlayers.value.filter(tp => 
          !(tp.teamId === teamId && tp.playerId === playerId)
        )
      }

      return result
    } catch (err) {
      error.value = String(err)
      return {
        success: false,
        error: String(err),
        timestamp: new Date().toISOString()
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * Clear all data (useful for testing)
   */
  function clearData(): void {
    teams.value = []
    players.value = []
    teamPlayers.value = []
    error.value = null
  }

  // ============================================================================
  // RETURN STORE
  // ============================================================================

  return {
    // State
    teams,
    teamPlayers,
    players,
    loading,
    error,

    // Getters
    teamsWithRelations,
    getTeamById,
    getTeamWithRelationsById,
    getTeamPlayers,
    getTeamsBySeason,
    allSeasons,

    // Actions
    loadTeams,
    createTeam,
    updateTeam,
    deleteTeam,
    addPlayerToTeam,
    removePlayerFromTeam,
    clearData
  }
})
