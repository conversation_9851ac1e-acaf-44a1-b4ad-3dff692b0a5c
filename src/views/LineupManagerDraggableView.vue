<script setup lang="ts">
import { ref, computed } from 'vue'
import LineupGrid from '@/components/LineupGrid.vue'
import DraggableRoster from '@/components/DraggableRoster.vue'
import GameSettingsDialog from '@/components/GameSettingsDialog.vue'
import { useMockData } from '@/composables/useMockData'
import type { Team, Game } from '@/types'

// Use mock data composable
const {
  teams,
  selectedTeam,
  selectedGame,
  navigationState,
  selectTeam,
  selectGame,
  goBackToTeams,
  goBackToGames
} = useMockData()

// Lineup state
const activePlayerId = ref<string | null>(null)
const lineupGridRef = ref<InstanceType<typeof LineupGrid> | null>(null)
const showReplacementDialog = ref(false)
const replacementDialogData = ref<{
  inning: number;
  position: string;
  existingPlayerId: string;
  newPlayerId: string;
} | null>(null)

// Player selection and highlighting
const handlePlayerSelect = (playerId: string) => {
  // Toggle active player selection
  if (activePlayerId.value === playerId) {
    activePlayerId.value = null
  } else {
    activePlayerId.value = playerId
  }

  // Highlight player in grid if they become active
  if (lineupGridRef.value && activePlayerId.value) {
    lineupGridRef.value.highlightPlayer(activePlayerId.value)
  }
}

// Drag and drop handling
const currentDragData = ref<any>(null)
const isDragging = ref(false)

const handleDragStart = (data?: any) => {
  isDragging.value = true
  currentDragData.value = data
}

const handleDragEnd = () => {
  isDragging.value = false
  currentDragData.value = null
}

// Cell click handling for player assignment
const handleCellClick = (data: { inning: number, position: string, currentPlayerId?: string | null }) => {
  if (!activePlayerId.value) {
    // No active player selected, show instruction
    return
  }

  const { inning, position, currentPlayerId } = data

  if (!currentPlayerId) {
    // Empty cell - assign active player
    assignPlayerToCell(inning, position, activePlayerId.value)
  } else if (currentPlayerId === activePlayerId.value) {
    // Cell has active player - remove them
    assignPlayerToCell(inning, position, null)
  } else {
    // Cell has different player - ask for confirmation
    replacementDialogData.value = {
      inning,
      position,
      existingPlayerId: currentPlayerId,
      newPlayerId: activePlayerId.value
    }
    showReplacementDialog.value = true
  }
}

// Player assignment to cell
const assignPlayerToCell = (inning: number, position: string, playerId: string | null) => {
  if (lineupGridRef.value && lineupGridRef.value.updateCell) {
    lineupGridRef.value.updateCell(inning, position, playerId)
  }
}

// Replacement confirmation handlers
const confirmReplacement = () => {
  if (replacementDialogData.value) {
    const { inning, position, newPlayerId } = replacementDialogData.value
    assignPlayerToCell(inning, position, newPlayerId)
  }
  closeReplacementDialog()
}

const closeReplacementDialog = () => {
  showReplacementDialog.value = false
  replacementDialogData.value = null
}

// Cell change handling (legacy - keeping for compatibility)
const handleCellChange = (data: { inning: number, position: string, playerId: string | null }) => {
  // Handle any additional cell change logic if needed
  console.log('Cell changed:', data)
}

// Player counts
const playerCounts = computed(() => {
  if (!lineupGridRef.value || !lineupGridRef.value.getPlayerCount) {
    return {}
  }
  return lineupGridRef.value.getPlayerCount.value || {}
})

// Game settings dialog state
const showGameSettingsDialog = ref(false)

// Notification state (simplified for draggable view)
const showNotification = ref(false)
const notificationText = ref('')
const notificationColor = ref('success')

// Show notification helper
const showNotify = (message: string, color: string = 'success') => {
  notificationText.value = message
  notificationColor.value = color
  showNotification.value = true
  setTimeout(() => {
    showNotification.value = false
  }, 3000)
}

// Game settings handlers
const openGameSettings = () => {
  showGameSettingsDialog.value = true
}

const handleGameSettingsSave = (gameData: Partial<Game>) => {
  if (selectedGame.value && selectedTeam.value) {
    // Update the current game with new settings
    Object.assign(selectedGame.value, gameData)

    showNotify('Game settings updated successfully', 'success')

    // Force reactive updates if needed
    if (lineupGridRef.value && gameData.inningCount && gameData.inningCount !== selectedGame.value.inningCount) {
      // The grid will automatically react to the inningCount change
      showNotify(`Lineup updated for ${gameData.inningCount} innings`, 'info')
    }
  }
}
</script>

<template>
  <div class="lineup-manager">
    <v-row no-gutters style="height: 100vh;">
      <!-- Sidebar -->
      <v-col cols="12" md="3" class="sidebar">
        <v-card flat tile height="100%" class="d-flex flex-column">
          <v-toolbar color="primary" dark flat>
            <v-btn
              v-if="navigationState !== 'teams'"
              icon
              @click="navigationState === 'lineup' ? goBackToGames() : goBackToTeams()"
              class="mr-2"
            >
              <v-icon>mdi-arrow-left</v-icon>
            </v-btn>
            <v-toolbar-title>
              {{
                navigationState === 'lineup' ? 'Games' :
                navigationState === 'games' ? selectedTeam?.name :
                'Teams'
              }}
            </v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn icon>
              <v-icon>
                {{
                  navigationState === 'lineup' ? 'mdi-account-plus' :
                  navigationState === 'games' ? 'mdi-calendar-plus' :
                  'mdi-plus'
                }}
              </v-icon>
            </v-btn>
          </v-toolbar>

          <!-- Teams List -->
          <v-list v-if="navigationState === 'teams'">
            <v-list-item
              v-for="team in teams"
              :key="team.id"
              :title="team.name"
              :subtitle="`${team.players.length} players • ${team.games.length} games • ${team.season}`"
              @click="selectTeam(team)"
              class="cursor-pointer"
            >
              <template v-slot:prepend>
                <v-avatar color="primary">
                  <v-icon>mdi-account-group</v-icon>
                </v-avatar>
              </template>
              <template v-slot:append>
                <v-icon>mdi-chevron-right</v-icon>
              </template>
            </v-list-item>
          </v-list>

          <!-- Games List -->
          <v-list v-else-if="navigationState === 'games' && selectedTeam">
            <v-list-subheader>
              {{ selectedTeam.headCoach }} • {{ selectedTeam.season }} • {{ selectedTeam.games.length }} games
            </v-list-subheader>
            <v-list-item
              v-for="game in selectedTeam.games"
              :key="game.id"
              :title="`vs ${game.opponent}`"
              :subtitle="`${new Date(game.date).toLocaleDateString()} • ${game.gameTime || 'TBD'} • ${game.homeAway}`"
              @click="selectGame(game)"
              class="cursor-pointer"
            >
              <template v-slot:prepend>
                <v-avatar :color="game.homeAway === 'Home' ? 'green' : 'orange'" size="small">
                  <v-icon size="small">{{ game.homeAway === 'Home' ? 'mdi-home' : 'mdi-airplane' }}</v-icon>
                </v-avatar>
              </template>
              <template v-slot:append>
                <v-icon>mdi-chevron-right</v-icon>
              </template>
            </v-list-item>
          </v-list>

          <!-- Players List (when lineup view) -->
          <DraggableRoster
            v-if="navigationState === 'lineup' && selectedTeam && selectedGame"
            :players="selectedTeam.players"
            :selected-game="selectedGame"
            :active-player-id="activePlayerId"
            :player-counts="playerCounts"
            @player-select="handlePlayerSelect"
            @drag-start="handleDragStart"
            @drag-end="handleDragEnd"
          />
        </v-card>
      </v-col>

      <!-- Main content -->
      <v-col cols="12" md="9" class="main-content">
        <v-card flat tile height="100%" class="d-flex flex-column">
          <v-toolbar color="grey-lighten-3" flat>
            <v-toolbar-title>Lineup</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="primary" class="mr-2">
              <v-icon start>mdi-content-save</v-icon>
              Save
            </v-btn>
            <v-btn color="secondary">
              <v-icon start>mdi-printer</v-icon>
              Print
            </v-btn>
          </v-toolbar>

          <v-card-text class="flex-grow-1 overflow-y-auto">
            <!-- Teams View -->
            <div v-if="navigationState === 'teams'">
              <h2 class="text-h5 mb-4">Select a team to manage</h2>
              <v-alert type="info" variant="tonal">
                <template v-slot:prepend>
                  <v-icon>mdi-information</v-icon>
                </template>
                <div>Choose a team from the sidebar to view games and create lineups.</div>
              </v-alert>
            </div>

            <!-- Games View -->
            <div v-else-if="navigationState === 'games' && selectedTeam">
              <h2 class="text-h5 mb-2">{{ selectedTeam.name }}</h2>
              <p class="text-subtitle-1 text-medium-emphasis mb-4">
                {{ selectedTeam.headCoach }} • {{ selectedTeam.season }}
              </p>
              <h3 class="text-h6 mb-3">Upcoming Games</h3>
              <v-alert type="info" variant="tonal" class="mb-4">
                <template v-slot:prepend>
                  <v-icon>mdi-information</v-icon>
                </template>
                <div>Select a game from the sidebar to create or edit its lineup.</div>
              </v-alert>

              <div class="d-flex flex-wrap gap-4">
                <v-card
                  v-for="game in selectedTeam.games"
                  :key="game.id"
                  class="game-card"
                  elevation="2"
                  @click="selectGame(game)"
                  style="cursor: pointer; min-width: 300px;"
                >
                  <v-card-title class="d-flex align-center">
                    <v-icon :color="game.homeAway === 'Home' ? 'green' : 'orange'" class="mr-2">
                      {{ game.homeAway === 'Home' ? 'mdi-home' : 'mdi-airplane' }}
                    </v-icon>
                    vs {{ game.opponent }}
                  </v-card-title>
                  <v-card-text>
                    <div><strong>Date:</strong> {{ new Date(game.date).toLocaleDateString() }}</div>
                    <div v-if="game.gameTime"><strong>Game Time:</strong> {{ game.gameTime }}</div>
                    <div v-if="game.arrivalTime"><strong>Arrival:</strong> {{ game.arrivalTime }}</div>
                    <div><strong>Location:</strong> {{ game.homeAway }}</div>
                    <div v-if="game.address" class="text-caption mt-1">{{ game.address }}</div>
                    <div v-if="game.notes" class="text-caption mt-2 font-italic">{{ game.notes }}</div>
                  </v-card-text>
                </v-card>
              </div>
            </div>

            <!-- Lineup View -->
            <div v-else-if="navigationState === 'lineup' && selectedGame && selectedTeam" class="lineup-view">
              <div class="lineup-header">
                <h2 class="text-h5 mb-2 d-flex align-center">
                  {{ selectedTeam.name }} vs {{ selectedGame.opponent }}
                  <v-btn
                    icon
                    size="small"
                    variant="text"
                    class="ml-2"
                    @click="openGameSettings"
                  >
                    <v-icon>mdi-cog</v-icon>
                  </v-btn>
                </h2>
                <p class="text-subtitle-1 text-medium-emphasis mb-4">
                  {{ new Date(selectedGame.date).toLocaleDateString() }} • {{ selectedGame.gameTime || 'TBD' }} • {{ selectedGame.homeAway }}
                </p>
              </div>

              <LineupGrid
                ref="lineupGridRef"
                :game="selectedGame"
                :players="selectedTeam.players"
                :active-player-id="activePlayerId"
                :show-keyboard-hints="true"
                @player-select="handlePlayerSelect"
                @cell-click="handleCellClick"
                @cell-change="handleCellChange"
                @drag-start="handleDragStart"
                @drag-end="handleDragEnd"
                class="flex-grow-1"
              />
            </div>
          </v-card-text>

          <v-divider></v-divider>

          <v-card-actions class="pa-4">
            <v-spacer></v-spacer>
            <v-btn color="primary" variant="tonal">
              <v-icon start>mdi-shuffle</v-icon>
              Auto-Fill
            </v-btn>
            <v-btn color="error" variant="tonal">
              <v-icon start>mdi-delete</v-icon>
              Clear All
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- Player Replacement Confirmation Dialog -->
    <v-dialog v-model="showReplacementDialog" max-width="400" persistent>
      <v-card>
        <v-card-title class="text-h6">Replace Player?</v-card-title>
        <v-card-text v-if="replacementDialogData && selectedTeam">
          <p>
            Position <strong>{{ replacementDialogData.position }}</strong> in
            <strong>Inning {{ replacementDialogData.inning }}</strong> currently has:
          </p>
          <v-chip
            color="error"
            variant="outlined"
            class="mb-2"
          >
            {{ selectedTeam.players.find(p => p.id === replacementDialogData?.existingPlayerId)?.firstName }}
            {{ selectedTeam.players.find(p => p.id === replacementDialogData?.existingPlayerId)?.lastName }}
          </v-chip>
          <p class="mt-2">Replace with:</p>
          <v-chip
            color="success"
            variant="outlined"
          >
            {{ selectedTeam.players.find(p => p.id === replacementDialogData?.newPlayerId)?.firstName }}
            {{ selectedTeam.players.find(p => p.id === replacementDialogData?.newPlayerId)?.lastName }}
          </v-chip>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="closeReplacementDialog"
          >
            Cancel
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            @click="confirmReplacement"
          >
            Replace
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Game Settings Dialog -->
    <GameSettingsDialog
      v-model="showGameSettingsDialog"
      :game="selectedGame"
      :team-id="selectedTeam?.id"
      @save="handleGameSettingsSave"
    />

    <!-- Notification Snackbar -->
    <v-snackbar
      v-model="showNotification"
      :color="notificationColor"
      location="top"
      timeout="3000"
    >
      <v-icon start>
        {{
          notificationColor === 'success' ? 'mdi-check-circle' :
          notificationColor === 'info' ? 'mdi-information' :
          notificationColor === 'warning' ? 'mdi-alert' :
          'mdi-alert-circle'
        }}
      </v-icon>
      {{ notificationText }}

      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="showNotification = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<style scoped>
.lineup-manager {
  height: 100vh;
}

.sidebar {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  height: 100vh;
  overflow-y: auto;
}

.main-content {
  height: 100vh;
  overflow-y: auto;
}

.cursor-pointer {
  cursor: pointer;
}

.game-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease-in-out;
}

.lineup-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.lineup-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.flex-grow-1 {
  flex-grow: 1;
  min-height: 0;
}
</style>