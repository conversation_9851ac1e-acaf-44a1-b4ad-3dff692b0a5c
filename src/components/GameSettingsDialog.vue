<script setup lang="ts">
import { ref, computed } from 'vue'
import GameSettingsForm from './GameSettingsForm.vue'
import type { Game } from '@/types'

// Props
interface Props {
  modelValue: boolean
  game?: Game | null
  teamId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  game: null,
  teamId: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'save': [gameData: Partial<Game>]
}>()

// Local dialog state
const isOpen = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// Form state
const formData = ref<Partial<Game>>({})
const isFormValid = ref(false)
const gameSettingsFormRef = ref<InstanceType<typeof GameSettingsForm> | null>(null)

// Dialog confirmation state
const showConfirmClose = ref(false)

// Form data and validation handlers
const handleFormDataUpdate = (data: Partial<Game>) => {
  formData.value = data
}

const handleFormValidation = (valid: boolean) => {
  isFormValid.value = valid
}

// Save handler
const handleSave = async () => {
  if (gameSettingsFormRef.value) {
    await gameSettingsFormRef.value.validate()

    if (isFormValid.value) {
      emit('save', formData.value)
      isOpen.value = false
    }
  }
}

// Close handler with unsaved changes check
const handleClose = () => {
  if (gameSettingsFormRef.value?.hasChanges.value) {
    showConfirmClose.value = true
  } else {
    isOpen.value = false
  }
}

// Confirm close without saving
const confirmClose = () => {
  showConfirmClose.value = false
  isOpen.value = false
}

// Cancel close confirmation
const cancelClose = () => {
  showConfirmClose.value = false
}

// Dialog title
const dialogTitle = computed(() => {
  return props.game ? 'Game Settings' : 'New Game Settings'
})
</script>

<template>
  <v-dialog
    v-model="isOpen"
    max-width="800"
    persistent
    scrollable
  >
    <v-card>
      <v-card-title class="d-flex align-center">
        <v-icon class="mr-3">mdi-cog</v-icon>
        {{ dialogTitle }}
        <v-spacer></v-spacer>
        <v-btn
          icon
          variant="text"
          @click="handleClose"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-divider></v-divider>

      <v-card-text class="pa-6">
        <GameSettingsForm
          ref="gameSettingsFormRef"
          :game="game"
          :team-id="teamId"
          @update:form-data="handleFormDataUpdate"
          @form-valid="handleFormValidation"
        />
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions class="pa-4">
        <v-spacer></v-spacer>

        <v-btn
          color="grey-darken-1"
          variant="text"
          @click="handleClose"
        >
          <v-icon start>mdi-close</v-icon>
          Close
        </v-btn>

        <v-btn
          color="primary"
          variant="elevated"
          :disabled="!isFormValid"
          @click="handleSave"
        >
          <v-icon start>mdi-content-save</v-icon>
          Save
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- Unsaved Changes Confirmation Dialog -->
    <v-dialog
      v-model="showConfirmClose"
      max-width="400"
      persistent
    >
      <v-card>
        <v-card-title class="text-h6">
          <v-icon color="warning" class="mr-2">mdi-alert</v-icon>
          Unsaved Changes
        </v-card-title>

        <v-card-text>
          <p>You have unsaved changes to the game settings.</p>
          <p>Are you sure you want to close without saving?</p>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>

          <v-btn
            color="primary"
            variant="text"
            @click="cancelClose"
          >
            Cancel
          </v-btn>

          <v-btn
            color="warning"
            variant="elevated"
            @click="confirmClose"
          >
            <v-icon start>mdi-delete</v-icon>
            Discard Changes
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-dialog>
</template>

<style scoped>
.v-card-title {
  background-color: rgb(var(--v-theme-surface-variant));
}

.v-card-actions {
  background-color: rgb(var(--v-theme-surface-variant));
}
</style>