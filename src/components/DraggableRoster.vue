<template>
  <div class="draggable-roster">
    <v-list-subheader class="pl-4">
      {{ gameInfo }}
    </v-list-subheader>

    <!-- Instructions when no active player is selected -->
    <v-alert
      v-if="!activePlayerId"
      type="info"
      variant="tonal"
      density="compact"
      class="ma-2"
    >
      <template v-slot:prepend>
        <v-icon size="small">mdi-information</v-icon>
      </template>
      <div class="text-caption">
        Click a player below to select them, then click lineup positions to assign/remove.
      </div>
    </v-alert>

    <!-- Active player indicator -->
    <v-alert
      v-else
      color="primary"
      variant="tonal"
      density="compact"
      class="ma-2"
    >
      <template v-slot:prepend>
        <v-icon size="small">mdi-account-check</v-icon>
      </template>
      <div class="text-caption">
        <strong>{{ getActivePlayerName() }}</strong> selected. Click lineup positions to assign/remove.
      </div>
    </v-alert>

    <draggable
      :list="rosterPlayers"
      :group="{ name: 'lineup', pull: 'clone', put: false }"
      item-key="id"
      :clone="clonePlayer"
      :sort="false"
      class="roster-list"
      role="list"
      aria-label="Player roster - drag players to lineup grid"
    >
      <template #item="{ element: player }">
        <v-list-item
          :key="player.id"
          :data-player-id="player.id"
          class="roster-item"
          :class="{
            'player-selected': activePlayerId === player.id,
            'player-active': (playerCounts && playerCounts[player.id]) > 0,
            'player-overused': (playerCounts && playerCounts[player.id]) > maxInnings * 0.8
          }"
          role="listitem"
          :aria-label="`${player.firstName} ${player.lastName || ''}, currently assigned to ${(playerCounts && playerCounts[player.id]) || 0} innings`"
          :aria-selected="activePlayerId === player.id"
          tabindex="0"
          @click="handlePlayerClick(player)"
          @keydown.enter="handlePlayerClick(player)"
          @keydown.space.prevent="handlePlayerClick(player)"
        >
          <template v-slot:prepend>
            <v-avatar color="secondary" size="small">
              <span class="text-caption">
                {{ player.firstName[0] }}{{ (player.lastName || '')[0] || '' }}
              </span>
            </v-avatar>
          </template>

          <v-list-item-title>
            {{ player.firstName }} {{ player.lastName || '' }}
          </v-list-item-title>

          <v-list-item-subtitle>
            <span v-if="player.throws || player.bats">
              <span v-if="player.throws">Throws: {{ player.throws }}</span>
              <span v-if="player.throws && player.bats"> • </span>
              <span v-if="player.bats">Bats: {{ player.bats }}</span>
            </span>
            <span v-else>-</span>
          </v-list-item-subtitle>

          <template v-slot:append>
            <v-chip
              v-if="playerCounts && playerCounts[player.id] > 0"
              :color="getChipColor(playerCounts[player.id])"
              size="small"
              variant="flat"
            >
              {{ playerCounts[player.id] }}
            </v-chip>
            <v-icon class="drag-handle" size="small" color="grey">
              mdi-drag-vertical
            </v-icon>
          </template>
        </v-list-item>
      </template>
    </draggable>

    <!-- Player usage legend -->
    <div v-if="showUsageLegend" class="usage-legend">
      <v-divider class="my-2" />
      <div class="legend-title text-caption text-medium-emphasis mb-1">
        Player Usage
      </div>
      <div class="legend-items">
        <div class="legend-item">
          <v-chip size="x-small" color="default" variant="flat">0</v-chip>
          <span class="text-caption ml-1">Benched</span>
        </div>
        <div class="legend-item">
          <v-chip size="x-small" color="success" variant="flat">1-8</v-chip>
          <span class="text-caption ml-1">Active</span>
        </div>
        <div class="legend-item">
          <v-chip size="x-small" color="warning" variant="flat">9+</v-chip>
          <span class="text-caption ml-1">Heavy</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import draggable from 'vuedraggable'
import type { Player, Game } from '../types'

interface Props {
  players: Player[]
  selectedGame: Game
  activePlayerId?: string | null
  playerCounts: Record<string, number>
  showUsageLegend?: boolean
}

interface Emits {
  (e: 'player-select', playerId: string): void
  (e: 'drag-start', data: { playerId: string, sourceType: 'roster' }): void
  (e: 'drag-end'): void
}

const props = withDefaults(defineProps<Props>(), {
  activePlayerId: null,
  showUsageLegend: true
})

const emit = defineEmits<Emits>()

// Make roster immutable - use computed to always reflect props.players
const rosterPlayers = computed(() => props.players)

const maxInnings = computed(() => props.selectedGame?.inningCount || 12)

const gameInfo = computed(() => {
  if (!props.selectedGame) return 'No game selected'
  return `${props.selectedGame.opponent} • ${new Date(props.selectedGame.date).toLocaleDateString()}`
})

const clonePlayer = (player: Player): any => {
  // Return a copy for drag operations with additional properties for grid compatibility
  return {
    ...player,
    // Add name property for consistency with grid data structure
    name: `${player.firstName} ${player.lastName || ''}`.trim()
  }
}

const getChipColor = (count: number): string => {
  if (!count || count === 0) return 'default'
  if (count > maxInnings.value * 0.8) return 'warning'
  return 'success'
}

const handlePlayerClick = (player: Player) => {
  emit('player-select', player.id)
}

const getActivePlayerName = (): string => {
  if (!props.activePlayerId) return ''
  const player = props.players.find(p => p.id === props.activePlayerId)
  if (!player) return ''
  return `${player.firstName} ${player.lastName || ''}`.trim()
}

// Drag handlers removed - letting vuedraggable handle cloning naturally

// Roster is now immutable and always reflects props.players
// No need for updateRosterOrder since we use computed property

defineExpose({
  // No longer expose updateRosterOrder since roster is immutable
})
</script>

<style scoped>
.draggable-roster {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.roster-list {
  flex-grow: 1;
  overflow-y: auto;
}

.roster-item {
  cursor: grab;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 1px 4px;
}

.roster-item:hover {
  background: rgba(0, 0, 0, 0.04);
}

.roster-item.player-selected {
  background: rgba(33, 150, 243, 0.15) !important;
  border: 2px solid rgba(33, 150, 243, 0.5);
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.roster-item.player-active {
  border-left: 3px solid #4caf50;
}

.roster-item.player-overused {
  border-left: 3px solid #ff9800;
}

.drag-handle {
  cursor: grab;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.roster-item:hover .drag-handle {
  opacity: 1;
}

.roster-item.sortable-ghost {
  opacity: 0.5;
}

.roster-item.sortable-chosen {
  background: rgba(25, 118, 210, 0.1);
}

.usage-legend {
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
}

.legend-title {
  font-weight: 500;
}

.legend-items {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Sortable specific styles */
:deep(.sortable-drag) {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  transform: rotate(2deg);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .roster-item {
    transition: none;
  }
}
</style>