<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { Game } from '@/types'

// Props
interface Props {
  game?: Game | null
  teamId?: string
}

const props = withDefaults(defineProps<Props>(), {
  game: null,
  teamId: ''
})

// Emits
const emit = defineEmits<{
  'update:form-data': [value: Partial<Game>]
  'form-valid': [value: boolean]
}>()

// Form data
const formData = reactive<Partial<Game>>({
  date: '',
  gameTime: '',
  arrivalTime: '',
  opponent: '',
  homeAway: 'Home',
  address: '',
  notes: '',
  inningCount: 7
})

// Form validation
const rules = {
  required: (value: string) => !!value || 'Field is required',
  opponent: (value: string) => !!value || 'Opponent is required',
  date: (value: string) => !!value || 'Date is required',
  inningCount: (value: number) => (value > 0 && value <= 15) || 'Innings must be between 1 and 15'
}

// Form ref for validation
const formRef = ref<any>(null)

// Home/Away options
const homeAwayOptions = [
  { title: 'Home', value: 'Home' },
  { title: 'Away', value: 'Away' }
]

// Innings options (common counts for baseball)
const inningOptions = [
  { title: '6 Innings', value: 6 },
  { title: '7 Innings', value: 7 },
  { title: '9 Innings', value: 9 },
  { title: '10 Innings', value: 10 },
  { title: '11 Innings', value: 11 },
  { title: '12 Innings', value: 12 }
]

// Initialize form data when game prop changes
watch(() => props.game, (newGame) => {
  if (newGame) {
    Object.assign(formData, {
      date: newGame.date || '',
      gameTime: newGame.gameTime || '',
      arrivalTime: newGame.arrivalTime || '',
      opponent: newGame.opponent || '',
      homeAway: newGame.homeAway || 'Home',
      address: newGame.address || '',
      notes: newGame.notes || '',
      inningCount: newGame.inningCount || 7
    })
  } else {
    // Reset to defaults for new game
    Object.assign(formData, {
      date: '',
      gameTime: '',
      arrivalTime: '',
      opponent: '',
      homeAway: 'Home',
      address: '',
      notes: '',
      inningCount: 7
    })
  }
  // Add teamId if provided
  if (props.teamId) {
    formData.teamId = props.teamId
  }
}, { immediate: true })

// Watch form data changes and emit updates
watch(formData, (newFormData) => {
  emit('update:form-data', { ...newFormData })
  validateForm()
}, { deep: true })

// Form validation
const isFormValid = ref(false)

const validateForm = async () => {
  if (formRef.value) {
    const { valid } = await formRef.value.validate()
    isFormValid.value = valid
    emit('form-valid', valid)
  }
}

// Helper to check if form has changes
const hasChanges = computed(() => {
  if (!props.game) return true // New game always has changes

  return (
    formData.date !== (props.game.date || '') ||
    formData.gameTime !== (props.game.gameTime || '') ||
    formData.arrivalTime !== (props.game.arrivalTime || '') ||
    formData.opponent !== (props.game.opponent || '') ||
    formData.homeAway !== (props.game.homeAway || 'Home') ||
    formData.address !== (props.game.address || '') ||
    formData.notes !== (props.game.notes || '') ||
    formData.inningCount !== (props.game.inningCount || 7)
  )
})

// Expose methods for parent components
defineExpose({
  validate: validateForm,
  hasChanges,
  isValid: isFormValid
})
</script>

<template>
  <v-form ref="formRef" v-model="isFormValid">
    <v-row>
      <!-- Opponent -->
      <v-col cols="12" md="6">
        <v-text-field
          v-model="formData.opponent"
          label="Opponent Team"
          :rules="[rules.required, rules.opponent]"
          prepend-inner-icon="mdi-account-group"
          placeholder="Enter opponent team name"
          variant="outlined"
          density="comfortable"
          required
        />
      </v-col>

      <!-- Home/Away -->
      <v-col cols="12" md="6">
        <v-select
          v-model="formData.homeAway"
          :items="homeAwayOptions"
          label="Home / Away"
          prepend-inner-icon="mdi-home-account"
          variant="outlined"
          density="comfortable"
          required
        />
      </v-col>

      <!-- Date -->
      <v-col cols="12" md="6">
        <v-text-field
          v-model="formData.date"
          label="Game Date"
          :rules="[rules.required, rules.date]"
          type="date"
          prepend-inner-icon="mdi-calendar"
          variant="outlined"
          density="comfortable"
          required
        />
      </v-col>

      <!-- Game Time -->
      <v-col cols="12" md="6">
        <v-text-field
          v-model="formData.gameTime"
          label="Game Time"
          type="time"
          prepend-inner-icon="mdi-clock"
          placeholder="HH:MM"
          variant="outlined"
          density="comfortable"
          hint="Optional - game start time"
          persistent-hint
        />
      </v-col>

      <!-- Arrival Time -->
      <v-col cols="12" md="6">
        <v-text-field
          v-model="formData.arrivalTime"
          label="Arrival Time"
          type="time"
          prepend-inner-icon="mdi-clock-alert"
          placeholder="HH:MM"
          variant="outlined"
          density="comfortable"
          hint="Optional - team arrival time"
          persistent-hint
        />
      </v-col>

      <!-- Innings Count -->
      <v-col cols="12" md="6">
        <v-select
          v-model="formData.inningCount"
          :items="inningOptions"
          label="# Innings"
          :rules="[rules.inningCount]"
          prepend-inner-icon="mdi-numeric"
          variant="outlined"
          density="comfortable"
          hint="Number of innings to plan for"
          persistent-hint
          required
        />
      </v-col>

      <!-- Address -->
      <v-col cols="12">
        <v-text-field
          v-model="formData.address"
          label="Address / Location"
          prepend-inner-icon="mdi-map-marker"
          placeholder="Field address or location details"
          variant="outlined"
          density="comfortable"
          hint="Optional - game location details"
          persistent-hint
        />
      </v-col>

      <!-- Notes -->
      <v-col cols="12">
        <v-textarea
          v-model="formData.notes"
          label="Game Notes"
          prepend-inner-icon="mdi-note-text"
          placeholder="Any additional notes about the game..."
          variant="outlined"
          density="comfortable"
          rows="3"
          auto-grow
          hint="Optional - special instructions, weather concerns, etc."
          persistent-hint
        />
      </v-col>
    </v-row>
  </v-form>
</template>

<style scoped>
/* Any custom form styling if needed */
</style>