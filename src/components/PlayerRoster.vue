<script setup lang="ts">
import { computed } from 'vue'
import type { Player, Game } from '@/types'

interface Props {
  players: Player[]
  selectedGame: Game
  activePlayerId?: string | null
  playerCounts?: Record<string, number>
}

interface Emits {
  (e: 'player-select', playerId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Calculate player usage counts
const playerUsage = computed(() => {
  const counts: Record<string, number> = {}
  props.players.forEach(player => {
    counts[player.id] = props.playerCounts?.[player.id] || 0
  })
  return counts
})

const handlePlayerClick = (player: Player) => {
  console.log('🎯 Player clicked in PlayerRoster:', {
    playerId: player.id,
    playerName: getPlayerName(player),
    currentActivePlayerId: props.activePlayerId,
    isCurrentlyActive: props.activePlayerId === player.id,
    source: 'PlayerRoster.handlePlayerClick'
  })

  emit('player-select', player.id)

  console.log('✅ player-select event emitted from PlayerRoster for:', player.id)
}

// Get player display name
const getPlayerName = (player: Player) => {
  return `${player.firstName} ${player.lastName}`
}

// Get player info subtitle
const getPlayerInfo = (player: Player) => {
  const info = []
  if (player.throws) info.push(`T: ${player.throws}`)
  if (player.bats) info.push(`B: ${player.bats}`)
  return info.join(' • ')
}
</script>

<template>
  <div class="player-roster">
    <v-list>
      <v-list-subheader class="d-flex align-center">
        <v-icon class="mr-2">mdi-account-group</v-icon>
        Roster ({{ players.length }} players)
        <v-spacer></v-spacer>
        <v-chip color="blue" size="x-small" variant="outlined">
          <v-icon start size="x-small">mdi-cursor-default-click</v-icon>
          Click Mode
        </v-chip>
      </v-list-subheader>

      <v-list-item
        v-for="player in players"
        :key="player.id"
        :class="{ 'player-active': activePlayerId === player.id }"
        class="player-item cursor-pointer"
        @click="handlePlayerClick(player)"
      >
        <template v-slot:prepend>
          <v-avatar
            :color="activePlayerId === player.id ? 'primary' : 'grey-lighten-1'"
            size="small"
          >
            <span class="text-caption font-weight-bold">
              {{ player.firstName.charAt(0) }}{{ player.lastName.charAt(0) }}
            </span>
          </v-avatar>
        </template>

        <v-list-item-title class="player-name">
          {{ getPlayerName(player) }}
        </v-list-item-title>

        <v-list-item-subtitle v-if="getPlayerInfo(player)">
          {{ getPlayerInfo(player) }}
        </v-list-item-subtitle>

        <template v-slot:append>
          <v-chip
            v-if="playerUsage[player.id] > 0"
            :color="playerUsage[player.id] >= selectedGame.inningCount ? 'success' : 'warning'"
            size="small"
            variant="outlined"
          >
            {{ playerUsage[player.id] }}
          </v-chip>
          <v-icon
            v-if="activePlayerId === player.id"
            color="primary"
            size="small"
          >
            mdi-check-circle
          </v-icon>
        </template>
      </v-list-item>
    </v-list>

    <!-- Instructions -->
    <v-card flat class="ma-3">
      <v-card-text class="pa-3">
        <div class="text-caption text-medium-emphasis">
          <v-icon size="small" class="mr-1">mdi-information-outline</v-icon>
          Click a player to select, then click in the grid to place them.
        </div>
        <div v-if="activePlayerId" class="text-caption text-primary mt-1">
          <v-icon size="small" class="mr-1">mdi-cursor-default-click</v-icon>
          Player selected - click in grid to place
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<style scoped>
.player-roster {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.player-item {
  transition: all 0.2s ease-in-out;
}

.player-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.04);
}

.player-active {
  background-color: rgba(var(--v-theme-primary), 0.08) !important;
  border-left: 3px solid rgb(var(--v-theme-primary));
}

.player-active .player-name {
  font-weight: 600;
  color: rgb(var(--v-theme-primary));
}

.cursor-pointer {
  cursor: pointer;
}
</style>