{"name": "baseball-lineup", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@mdi/font": "^7.4.47", "@pinia/nuxt": "^0.11.2", "dexie": "^4.2.0", "pinia": "^3.0.3", "roboto-fontface": "^0.10.0", "uuid": "^13.0.0", "vite-plugin-vuetify": "^2.1.2", "vue": "^3.5.18", "vue-draggable-next": "^2.3.0", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0", "vuetify": "^3.10.0"}, "devDependencies": {"@types/node": "^24.3.3", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.1.1", "@vue/tsconfig": "^0.7.0", "sass": "^1.92.1", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}